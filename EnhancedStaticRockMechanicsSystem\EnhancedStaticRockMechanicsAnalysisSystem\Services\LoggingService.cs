using System;
using System.IO;
using System.Text;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Services
{
    /// <summary>
    /// 日志服务
    /// </summary>
    public class LoggingService
    {
        private static LoggingService? _instance;
        private static readonly object _lock = new object();
        private readonly string _logFilePath;
        private readonly object _fileLock = new object();

        private LoggingService()
        {
            // 创建日志目录
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // 设置日志文件路径
            string fileName = $"EnhancedStaticRockMechanics_{DateTime.Now:yyyyMMdd}.log";
            _logFilePath = Path.Combine(logDirectory, fileName);
        }

        /// <summary>
        /// 获取日志服务实例
        /// </summary>
        public static LoggingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LoggingService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message"></param>
        public void Info(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        /// <param name="message"></param>
        public void Warning(string message)
        {
            WriteLog("WARN", message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message"></param>
        public void Error(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 记录错误日志（包含异常信息）
        /// </summary>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        public void Error(string message, Exception exception)
        {
            string fullMessage = $"{message}\n异常详情: {exception}";
            WriteLog("ERROR", fullMessage);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message"></param>
        public void Debug(string message)
        {
#if DEBUG
            WriteLog("DEBUG", message);
#endif
        }

        /// <summary>
        /// 写入日志到文件
        /// </summary>
        /// <param name="level"></param>
        /// <param name="message"></param>
        private void WriteLog(string level, string message)
        {
            try
            {
                lock (_fileLock)
                {
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}";

                    // 写入文件
                    using (var writer = new StreamWriter(_logFilePath, true, Encoding.UTF8))
                    {
                        writer.WriteLine(logEntry);
                    }

                    // 同时输出到控制台（调试模式下）
#if DEBUG
                    Console.WriteLine(logEntry);
#endif
                }
            }
            catch (Exception ex)
            {
                // 日志写入失败时，输出到控制台
                Console.WriteLine($"日志写入失败: {ex.Message}");
                Console.WriteLine($"原始日志: [{level}] {message}");
            }
        }

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        /// <returns></returns>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }

        /// <summary>
        /// 清理旧日志文件（保留最近30天）
        /// </summary>
        public void CleanupOldLogs()
        {
            try
            {
                string logDirectory = Path.GetDirectoryName(_logFilePath);
                if (string.IsNullOrEmpty(logDirectory) || !Directory.Exists(logDirectory))
                    return;

                var files = Directory.GetFiles(logDirectory, "EnhancedStaticRockMechanics_*.log");
                var cutoffDate = DateTime.Now.AddDays(-30);

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(file);
                            Info($"删除旧日志文件: {Path.GetFileName(file)}");
                        }
                        catch (Exception ex)
                        {
                            Error($"删除日志文件失败: {Path.GetFileName(file)}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Error("清理旧日志文件时发生错误", ex);
            }
        }

        /// <summary>
        /// 记录系统启动信息
        /// </summary>
        public void LogSystemStart()
        {
            Info("=== 增强版静态岩石力学参数分析系统启动 ===");
            Info($"系统版本: {System.Reflection.Assembly.GetExecutingAssembly().GetName().Version}");
            Info($"运行环境: {Environment.OSVersion}");
            Info($".NET版本: {Environment.Version}");
            Info($"工作目录: {Environment.CurrentDirectory}");
        }

        /// <summary>
        /// 记录系统关闭信息
        /// </summary>
        public void LogSystemShutdown()
        {
            Info("=== 增强版静态岩石力学参数分析系统关闭 ===");

            // 清理历史对比图缓存（只在系统完全关闭时清理）
            CleanupHistoryComparisonCache();
        }

        /// <summary>
        /// 清理历史对比图缓存
        /// </summary>
        private void CleanupHistoryComparisonCache()
        {
            try
            {
                // 清理临时对比数据文件
                string tempPath = Path.GetTempPath();
                string[] tempFiles = {
                    Path.Combine(tempPath, "BritSystem_MineralogicalData.json"),
                    Path.Combine(tempPath, "BritSystem_StaticRockMechanicsData.json")
                };

                foreach (string file in tempFiles)
                {
                    if (File.Exists(file))
                    {
                        File.Delete(file);
                        Info($"已清理临时对比数据文件: {Path.GetFileName(file)}");
                    }
                }

                Info("历史对比图缓存清理完成");
            }
            catch (Exception ex)
            {
                Warning($"清理历史对比图缓存时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录用户操作
        /// </summary>
        /// <param name="username"></param>
        /// <param name="action"></param>
        /// <param name="details"></param>
        public void LogUserAction(string username, string action, string? details = null)
        {
            string message = $"用户操作 - 用户: {username}, 操作: {action}";
            if (!string.IsNullOrEmpty(details))
            {
                message += $", 详情: {details}";
            }
            Info(message);
        }

        /// <summary>
        /// 记录性能信息
        /// </summary>
        /// <param name="operation"></param>
        /// <param name="elapsedMilliseconds"></param>
        public void LogPerformance(string operation, long elapsedMilliseconds)
        {
            Info($"性能统计 - 操作: {operation}, 耗时: {elapsedMilliseconds}ms");
        }
    }
}
