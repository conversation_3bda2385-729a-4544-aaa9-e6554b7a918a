namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    partial class DashboardForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            lblTitle = new Label();
            pnlMain = new Panel();
            lblDescription = new Label();
            lblWelcome = new Label();
            pnlButtons = new Panel();
            btnExit = new Button();
            btnStaticRockMechanics = new Button();
            pnlMain.SuspendLayout();
            pnlButtons.SuspendLayout();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.BackColor = Color.FromArgb(45, 45, 45);
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Font = new Font("微软雅黑", 18F, FontStyle.Bold);
            lblTitle.ForeColor = Color.White;
            lblTitle.Location = new Point(0, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(800, 80);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "增强型煤储层静态岩石力学参数法脆性指数分析系统";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pnlMain
            // 
            pnlMain.BackColor = Color.FromArgb(33, 33, 33);
            pnlMain.Controls.Add(lblDescription);
            pnlMain.Controls.Add(lblWelcome);
            pnlMain.Controls.Add(pnlButtons);
            pnlMain.Dock = DockStyle.Fill;
            pnlMain.Location = new Point(0, 80);
            pnlMain.Name = "pnlMain";
            pnlMain.Size = new Size(800, 520);
            pnlMain.TabIndex = 1;
            // 
            // lblDescription
            // 
            lblDescription.Font = new Font("微软雅黑", 10F);
            lblDescription.ForeColor = Color.LightGray;
            lblDescription.Location = new Point(50, 100);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(700, 80);
            lblDescription.TabIndex = 1;
            lblDescription.Text = "本系统专门针对煤储层静态岩石力学参数计算脆性指数，提供专业的煤层地质分析功能。\r\n支持煤储层数据导入、参数计算、结果分析和图表展示等完整的分析流程。\r\n请选择相应的功能模块开始您的煤储层分析工作。";
            lblDescription.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblWelcome
            // 
            lblWelcome.Font = new Font("微软雅黑", 14F);
            lblWelcome.ForeColor = Color.White;
            lblWelcome.Location = new Point(50, 50);
            lblWelcome.Name = "lblWelcome";
            lblWelcome.Size = new Size(700, 40);
            lblWelcome.TabIndex = 0;
            lblWelcome.Text = "欢迎使用增强型煤储层静态岩石力学参数法脆性指数分析系统";
            lblWelcome.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pnlButtons
            // 
            pnlButtons.Controls.Add(btnExit);
            pnlButtons.Controls.Add(btnStaticRockMechanics);
            pnlButtons.Location = new Point(200, 220);
            pnlButtons.Name = "pnlButtons";
            pnlButtons.Size = new Size(400, 250);
            pnlButtons.TabIndex = 2;
            // 
            // btnExit
            // 
            btnExit.BackColor = Color.FromArgb(60, 60, 60);
            btnExit.FlatStyle = FlatStyle.Flat;
            btnExit.Font = new Font("微软雅黑", 10F);
            btnExit.ForeColor = Color.White;
            btnExit.Location = new Point(138, 120);
            btnExit.Name = "btnExit";
            btnExit.Size = new Size(115, 40);
            btnExit.TabIndex = 2;
            btnExit.Text = "退出系统";
            btnExit.UseVisualStyleBackColor = false;
            btnExit.Click += btnExit_Click;
            // 
            // btnStaticRockMechanics
            // 
            btnStaticRockMechanics.BackColor = Color.FromArgb(0, 120, 215);
            btnStaticRockMechanics.FlatStyle = FlatStyle.Flat;
            btnStaticRockMechanics.Font = new Font("微软雅黑", 12F);
            btnStaticRockMechanics.ForeColor = Color.White;
            btnStaticRockMechanics.Location = new Point(100, 30);
            btnStaticRockMechanics.Name = "btnStaticRockMechanics";
            btnStaticRockMechanics.Size = new Size(219, 50);
            btnStaticRockMechanics.TabIndex = 0;
            btnStaticRockMechanics.Text = "静态岩石力学参数分析";
            btnStaticRockMechanics.UseVisualStyleBackColor = false;
            btnStaticRockMechanics.Click += btnStaticRockMechanics_Click;
            // 
            // DashboardForm
            // 
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(800, 600);
            Controls.Add(pnlMain);
            Controls.Add(lblTitle);
            Font = new Font("微软雅黑", 9F);
            ForeColor = Color.White;
            MinimumSize = new Size(800, 600);
            Name = "DashboardForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "增强型煤储层静态岩石力学参数法脆性指数分析系统";
            FormClosing += DashboardForm_FormClosing;
            pnlMain.ResumeLayout(false);
            pnlButtons.ResumeLayout(false);
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.Label lblWelcome;
        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.Panel pnlButtons;
        private System.Windows.Forms.Button btnStaticRockMechanics;
        private System.Windows.Forms.Button btnExit;
    }
}
