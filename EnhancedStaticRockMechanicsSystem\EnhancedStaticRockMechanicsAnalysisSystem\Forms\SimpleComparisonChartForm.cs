using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;
using Newtonsoft.Json;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    /// <summary>
    /// 简化版对比图显示窗体 - 参考原系统ComparisonChartForm
    /// </summary>
    public partial class SimpleComparisonChartForm : Form
    {
        private Chart chartComparison;
        private Button btnClose;
        private Button btnSaveImage;
        private Button btnSeparate;
        private Button btnRestore;
        private Button btnImportData;
        private Button btnHistoryComparison; // 新增历史数据对比按钮
        private Button btnClearHistory; // 清除历史数据按钮
        private Label lblTitle;
        private Panel pnlControls;
        private bool isSeparated = false; // 标记是否处于分隔状态

        public SimpleComparisonChartForm()
        {
            InitializeComponent();
            InitializeChart();

            // 自动加载临时对比数据（如果存在）
            LoadTemporaryComparisonData();
        }

        private void InitializeComponent()
        {
            this.chartComparison = new Chart();
            this.btnClose = new Button();
            this.btnSaveImage = new Button();
            btnSeparate = new Button();
            btnRestore = new Button();
            btnImportData = new Button();
            btnClearHistory = new Button();
            lblTitle = new Label();
            pnlControls = new Panel();
            pnlControls.SuspendLayout();
            SuspendLayout();
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.FromArgb(50, 50, 50);
            btnClose.FlatAppearance.BorderColor = Color.Cyan;
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.ForeColor = Color.LightSkyBlue;
            btnClose.Location = new Point(1444, 24);
            btnClose.Margin = new Padding(4, 5, 4, 5);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(138, 48);
            btnClose.TabIndex = 1;
            btnClose.Text = "关闭";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += BtnClose_Click;
            // 
            // btnSaveImage
            // 
            btnSaveImage.BackColor = Color.FromArgb(50, 50, 50);
            btnSaveImage.FlatAppearance.BorderColor = Color.Cyan;
            btnSaveImage.FlatStyle = FlatStyle.Flat;
            btnSaveImage.ForeColor = Color.LightSkyBlue;
            btnSaveImage.Location = new Point(14, 24);
            btnSaveImage.Margin = new Padding(4, 5, 4, 5);
            btnSaveImage.Name = "btnSaveImage";
            btnSaveImage.Size = new Size(138, 48);
            btnSaveImage.TabIndex = 0;
            btnSaveImage.Text = "保存图像";
            btnSaveImage.UseVisualStyleBackColor = false;
            btnSaveImage.Click += BtnSaveImage_Click;
            // 
            // btnSeparate
            // 
            btnSeparate.BackColor = Color.FromArgb(50, 50, 50);
            btnSeparate.FlatAppearance.BorderColor = Color.Cyan;
            btnSeparate.FlatStyle = FlatStyle.Flat;
            btnSeparate.ForeColor = Color.LightSkyBlue;
            btnSeparate.Location = new Point(165, 24);
            btnSeparate.Margin = new Padding(4, 5, 4, 5);
            btnSeparate.Name = "btnSeparate";
            btnSeparate.Size = new Size(138, 48);
            btnSeparate.TabIndex = 2;
            btnSeparate.Text = "分隔显示";
            btnSeparate.UseVisualStyleBackColor = false;
            btnSeparate.Click += BtnSeparate_Click;
            // 
            // btnRestore
            // 
            btnRestore.BackColor = Color.FromArgb(50, 50, 50);
            btnRestore.FlatAppearance.BorderColor = Color.Cyan;
            btnRestore.FlatStyle = FlatStyle.Flat;
            btnRestore.ForeColor = Color.LightSkyBlue;
            btnRestore.Location = new Point(332, 24);
            btnRestore.Margin = new Padding(4, 5, 4, 5);
            btnRestore.Name = "btnRestore";
            btnRestore.Size = new Size(138, 48);
            btnRestore.TabIndex = 3;
            btnRestore.Text = "恢复显示";
            btnRestore.UseVisualStyleBackColor = false;
            btnRestore.Visible = false;
            btnRestore.Click += BtnRestore_Click;
            //
            // btnImportData
            //
            btnImportData.BackColor = Color.FromArgb(50, 50, 50);
            btnImportData.FlatAppearance.BorderColor = Color.Cyan;
            btnImportData.FlatStyle = FlatStyle.Flat;
            btnImportData.ForeColor = Color.LightSkyBlue;
            btnImportData.Location = new Point(483, 24);
            btnImportData.Margin = new Padding(4, 5, 4, 5);
            btnImportData.Name = "btnImportData";
            btnImportData.Size = new Size(138, 48);
            btnImportData.TabIndex = 4;
            btnImportData.Text = "导入数据";
            btnImportData.UseVisualStyleBackColor = false;
            btnImportData.Click += BtnImportData_Click;
            //
            // btnHistoryComparison
            //
            btnHistoryComparison = new Button();
            btnHistoryComparison.BackColor = Color.FromArgb(50, 50, 50);
            btnHistoryComparison.FlatAppearance.BorderColor = Color.Orange;
            btnHistoryComparison.FlatStyle = FlatStyle.Flat;
            btnHistoryComparison.ForeColor = Color.Orange;
            btnHistoryComparison.Location = new Point(634, 24);
            btnHistoryComparison.Margin = new Padding(4, 5, 4, 5);
            btnHistoryComparison.Name = "btnHistoryComparison";
            btnHistoryComparison.Size = new Size(158, 48);
            btnHistoryComparison.TabIndex = 5;
            btnHistoryComparison.Text = "历史数据对比";
            btnHistoryComparison.UseVisualStyleBackColor = false;
            btnHistoryComparison.Click += BtnHistoryComparison_Click;
            //
            // btnClearHistory
            //
            btnClearHistory = new Button();
            btnClearHistory.BackColor = Color.FromArgb(50, 50, 50);
            btnClearHistory.FlatAppearance.BorderColor = Color.Red;
            btnClearHistory.FlatStyle = FlatStyle.Flat;
            btnClearHistory.ForeColor = Color.Red;
            btnClearHistory.Location = new Point(800, 24);
            btnClearHistory.Margin = new Padding(4, 5, 4, 5);
            btnClearHistory.Name = "btnClearHistory";
            btnClearHistory.Size = new Size(120, 48);
            btnClearHistory.TabIndex = 6;
            btnClearHistory.Text = "清除历史";
            btnClearHistory.UseVisualStyleBackColor = false;
            btnClearHistory.Click += BtnClearHistory_Click;
            btnClearHistory.Visible = false; // 初始隐藏，只有加载历史数据后才显示
            //
            // lblTitle
            // 
            lblTitle.AutoSize = true;
            lblTitle.Font = new Font("Microsoft YaHei UI", 14F, FontStyle.Bold);
            lblTitle.ForeColor = Color.White;
            lblTitle.Location = new Point(16, 24);
            lblTitle.Margin = new Padding(4, 0, 4, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(325, 37);
            lblTitle.TabIndex = 1;
            lblTitle.Text = "脆性指数计算方法对比图";
            // 
            // pnlControls
            // 
            pnlControls.BackColor = Color.FromArgb(45, 45, 45);
            pnlControls.Controls.Add(btnSaveImage);
            pnlControls.Controls.Add(btnSeparate);
            pnlControls.Controls.Add(btnRestore);
            pnlControls.Controls.Add(btnImportData);
            pnlControls.Controls.Add(btnHistoryComparison);
            pnlControls.Controls.Add(btnClearHistory);
            pnlControls.Controls.Add(btnClose);
            pnlControls.Dock = DockStyle.Bottom;
            pnlControls.Location = new Point(0, 1184);
            pnlControls.Margin = new Padding(4, 5, 4, 5);
            pnlControls.Name = "pnlControls";
            pnlControls.Size = new Size(1650, 96);
            pnlControls.TabIndex = 2;
            // 
            //
            // chartComparison
            //
            chartComparison.BackColor = Color.FromArgb(45, 45, 45);
            chartComparison.BorderlineColor = Color.FromArgb(100, 100, 100);
            chartComparison.BorderlineDashStyle = ChartDashStyle.Solid;
            chartComparison.BorderlineWidth = 1;
            chartComparison.Location = new Point(16, 80);
            chartComparison.Name = "chartComparison";
            chartComparison.Size = new Size(1618, 1096);
            chartComparison.TabIndex = 3;
            chartComparison.Text = "chart1";
            //
            // SimpleComparisonChartForm
            //
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(33, 33, 33);
            ClientSize = new Size(1650, 1280);
            Controls.Add(chartComparison);
            Controls.Add(pnlControls);
            Controls.Add(lblTitle);
            ForeColor = Color.White;
            Margin = new Padding(4, 5, 4, 5);
            Name = "SimpleComparisonChartForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "脆性指数对比图";
            WindowState = FormWindowState.Maximized;
            pnlControls.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        private void InitializeChart()
        {
            // 清除现有内容
            chartComparison.Series.Clear();
            chartComparison.ChartAreas.Clear();
            chartComparison.Legends.Clear();

            // 创建图表区域 - 参考原系统ComparisonChartForm的布局
            ChartArea chartArea = new ChartArea("ComparisonArea");
            chartArea.BackColor = Color.FromArgb(45, 45, 45);

            // 设置图表区域位置和大小，确保不超出面板范围
            chartArea.Position = new ElementPosition(5, 5, 85, 90); // 左边距5%，上边距5%，宽度85%，高度90%
            chartArea.InnerPlotPosition = new ElementPosition(10, 10, 80, 80); // 内部绘图区域

            // 轴设置
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisX.MajorGrid.Enabled = true;
            chartArea.AxisY.MajorGrid.Enabled = true;
            chartArea.AxisY.IsReversed = true; // 深度轴反向显示

            // 设置轴范围，确保数据完全显示在图表内
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisX.Interval = 10;

            // 设置边距，确保标签和标题完全显示
            chartArea.AxisX.LabelStyle.Angle = 0;
            chartArea.AxisY.LabelStyle.Angle = 0;
            chartArea.AxisX.TitleAlignment = StringAlignment.Center;
            chartArea.AxisY.TitleAlignment = StringAlignment.Center;

            chartComparison.ChartAreas.Add(chartArea);

            // 创建图例 - 参考原系统的图例布局
            Legend legend = new Legend("ComparisonLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            legend.Docking = Docking.Right;
            legend.Alignment = StringAlignment.Center;
            legend.Position = new ElementPosition(90, 10, 10, 80); // 右侧10%宽度用于图例
            chartComparison.Legends.Add(legend);
        }

        /// <summary>
        /// 自动加载临时对比数据（如果存在）
        /// </summary>
        private void LoadTemporaryComparisonData()
        {
            try
            {
                // 检查临时文件是否存在
                string mineralDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_MineralogicalData.json");
                string staticDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");

                bool hasMineralData = File.Exists(mineralDataPath);
                bool hasStaticData = File.Exists(staticDataPath);

                if (hasMineralData || hasStaticData)
                {
                    LoggingService.Instance.Info($"发现临时对比数据 - 矿物组分法: {hasMineralData}, 煤储层静态岩石力学参数法: {hasStaticData}");
                    LoadComparisonData(mineralDataPath, staticDataPath, hasMineralData, hasStaticData);
                }
                else
                {
                    LoggingService.Instance.Info("没有发现临时对比数据");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"加载临时对比数据时出错: {ex.Message}");
            }
        }

        public void LoadComparisonData(string mineralDataPath, string staticDataPath, bool hasMineralData, bool hasStaticData)
        {
            try
            {
                int loadedSeries = 0;

                // 加载矿物组分法数据
                if (hasMineralData && File.Exists(mineralDataPath))
                {
                    LoadDataFromFile(mineralDataPath, Color.Blue, "矿物组分法");
                    loadedSeries++;
                }

                // 加载煤储层静态岩石力学参数法数据
                if (hasStaticData && File.Exists(staticDataPath))
                {
                    LoadDataFromFile(staticDataPath, Color.Cyan, "煤储层静态岩石力学参数法");
                    loadedSeries++;
                }

                // 自动调整Y轴刻度（关键修复 - 与原系统ComparisonChartForm一致）
                AdjustYAxisScale();

                // 更新标题
                if (loadedSeries > 0 && chartComparison?.Series != null)
                {
                    int totalPoints = chartComparison.Series.Sum(s => s?.Points?.Count ?? 0);
                    lblTitle.Text = $"脆性指数计算方法对比图 - 已加载 {loadedSeries} 个系统的数据，共 {totalPoints} 个数据点";
                }
                else
                {
                    lblTitle.Text = "脆性指数计算方法对比图 - 暂无数据";
                }

                // 确保按钮状态正确 - 参考原系统ComparisonChartForm
                if (loadedSeries >= 2)
                {
                    btnSeparate.Visible = true;
                    btnRestore.Visible = false;
                    isSeparated = false;
                }
                else
                {
                    btnSeparate.Visible = false;
                    btnRestore.Visible = false;
                }

                LoggingService.Instance.Info($"对比图数据加载完成，共 {loadedSeries} 个系列");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载对比数据失败: {ex.Message}");
                MessageBox.Show($"加载对比数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadDataFromFile(string filePath, Color seriesColor, string seriesName)
        {
            try
            {
                LoggingService.Instance.Info($"开始加载文件: {filePath}");

                if (!File.Exists(filePath))
                {
                    LoggingService.Instance.Warning($"文件不存在: {filePath}");
                    return;
                }

                if (chartComparison == null)
                {
                    LoggingService.Instance.Error("chartComparison为null");
                    return;
                }

                string jsonContent = File.ReadAllText(filePath);
                LoggingService.Instance.Info($"文件内容长度: {jsonContent.Length}");

                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    LoggingService.Instance.Warning("文件内容为空");
                    return;
                }

                dynamic data = JsonConvert.DeserializeObject(jsonContent);

                if (data == null)
                {
                    LoggingService.Instance.Warning("JSON反序列化结果为null");
                    return;
                }

                if (data.DataPoints == null)
                {
                    LoggingService.Instance.Warning("DataPoints为null");
                    return;
                }

                // 创建系列，根据系统类型设置不同样式（与原系统ComparisonChartForm完全一致）
                Series series = new Series(seriesName);

                if (seriesName.Contains("静态岩石力学参数法") || seriesName.Contains("增强型煤储层静态岩石力学参数法"))
                {
                    // 静态岩石力学参数法：使用平滑曲线，不显示数据点（与StaticRockMechanicsForm一致）
                    series.ChartType = SeriesChartType.Spline;
                    series.Color = Color.Cyan; // 使用青色，与原系统一致
                    series.BorderWidth = 2;
                    series.MarkerStyle = MarkerStyle.None; // 不显示数据点
                    series.MarkerSize = 0;
                }
                else
                {
                    // 矿物组分法：使用带数据点的线条
                    series.ChartType = SeriesChartType.Line;
                    series.Color = seriesColor;
                    series.BorderWidth = 3;
                    series.MarkerStyle = MarkerStyle.Circle;
                    series.MarkerSize = 8;
                    series.MarkerColor = seriesColor;
                    series.MarkerBorderColor = Color.White;
                    series.MarkerBorderWidth = 1;
                }

                series.IsVisibleInLegend = true;

                // 添加数据点
                int pointCount = 0;
                foreach (var point in data.DataPoints)
                {
                    try
                    {
                        if (point?.BrittleIndex != null && point?.TopDepth != null)
                        {
                            double brittleIndex = Convert.ToDouble(point.BrittleIndex);
                            double depth = Convert.ToDouble(point.TopDepth);

                            if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                            {
                                series.Points.AddXY(brittleIndex, depth);
                                pointCount++;
                            }
                        }
                    }
                    catch (Exception pointEx)
                    {
                        LoggingService.Instance.Warning($"处理数据点时出错: {pointEx.Message}");
                    }
                }

                if (chartComparison.Series == null)
                {
                    LoggingService.Instance.Error("chartComparison.Series为null");
                    return;
                }

                chartComparison.Series.Add(series);
                LoggingService.Instance.Info($"已加载 {seriesName} 数据，共 {pointCount} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载文件 {filePath} 失败: {ex.Message}");
            }
        }

        private void BtnSaveImage_Click(object sender, EventArgs e)
        {
            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "PNG图像|*.png|JPEG图像|*.jpg|所有文件|*.*";
                saveDialog.Title = "保存对比图";
                saveDialog.FileName = $"脆性指数对比图_{DateTime.Now:yyyyMMdd_HHmmss}";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    chartComparison.SaveImage(saveDialog.FileName, ChartImageFormat.Png);
                    MessageBox.Show($"图像已保存到: {saveDialog.FileName}", "保存成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoggingService.Instance.Info($"对比图已保存到: {saveDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"保存对比图失败: {ex.Message}");
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 分隔显示按钮事件 - 参考原系统ComparisonChartForm
        /// </summary>
        private void BtnSeparate_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartComparison.Series.Count == 0)
                    return;

                // 清除现有图表区域
                chartComparison.ChartAreas.Clear();

                // 为每个系列创建单独的图表区域 - 参考原系统ComparisonChartForm
                int areaIndex = 0;
                int seriesCount = chartComparison.Series.Count;
                float areaHeight = 90f / seriesCount; // 每个区域的高度百分比
                float topMargin = 5f; // 顶部边距

                foreach (var series in chartComparison.Series)
                {
                    var chartArea = new ChartArea($"Area{areaIndex}")
                    {
                        BackColor = Color.FromArgb(40, 40, 40),
                        // 确保每个区域都在面板范围内，留出边距
                        Position = new ElementPosition(5, topMargin + areaIndex * areaHeight, 85, areaHeight - 2),
                        InnerPlotPosition = new ElementPosition(15, 10, 70, 80) // 内部绘图区域
                    };

                    // 设置轴样式
                    chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                    chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                    chartArea.AxisX.LineColor = Color.Gray;
                    chartArea.AxisY.LineColor = Color.Gray;
                    chartArea.AxisX.MajorGrid.LineColor = Color.LightGray;
                    chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
                    chartArea.AxisX.MajorGrid.Enabled = true;
                    chartArea.AxisY.MajorGrid.Enabled = true;
                    chartArea.AxisY.IsReversed = true;

                    // 设置轴范围
                    chartArea.AxisX.Minimum = 0;
                    chartArea.AxisX.Maximum = 100;
                    chartArea.AxisX.Interval = 20;

                    // 只在最后一个区域显示X轴标题
                    if (areaIndex == seriesCount - 1)
                    {
                        chartArea.AxisX.Title = "脆性指数 (%)";
                        chartArea.AxisX.TitleForeColor = Color.White;
                    }

                    chartArea.AxisY.Title = $"{series.Name}";
                    chartArea.AxisY.TitleForeColor = Color.White;

                    chartComparison.ChartAreas.Add(chartArea);
                    series.ChartArea = chartArea.Name;
                    areaIndex++;
                }

                // 应用Y轴自适应刻度到所有分割的图表区域
                AdjustYAxisScaleForAllAreas();

                isSeparated = true;
                btnSeparate.Visible = false;
                btnRestore.Visible = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"分隔显示失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 恢复显示按钮事件 - 参考原系统ComparisonChartForm
        /// </summary>
        private void BtnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                if (chartComparison.Series.Count == 0)
                    return;

                // 清除现有图表区域
                chartComparison.ChartAreas.Clear();

                // 创建单一图表区域 - 与InitializeChart保持一致
                var chartArea = new ChartArea("MainArea")
                {
                    BackColor = Color.FromArgb(45, 45, 45),
                    Position = new ElementPosition(5, 5, 85, 90),
                    InnerPlotPosition = new ElementPosition(10, 10, 80, 80)
                };

                // 设置轴样式
                chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                chartArea.AxisX.LineColor = Color.Gray;
                chartArea.AxisY.LineColor = Color.Gray;
                chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
                chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
                chartArea.AxisX.MajorGrid.Enabled = true;
                chartArea.AxisY.MajorGrid.Enabled = true;
                chartArea.AxisX.Title = "脆性指数 (%)";
                chartArea.AxisY.Title = "深度 (m)";
                chartArea.AxisX.TitleForeColor = Color.White;
                chartArea.AxisY.TitleForeColor = Color.White;
                chartArea.AxisY.IsReversed = true;

                // 设置轴范围
                chartArea.AxisX.Minimum = 0;
                chartArea.AxisX.Maximum = 100;
                chartArea.AxisX.Interval = 10;

                chartComparison.ChartAreas.Add(chartArea);

                // 将所有系列分配到同一个图表区域
                foreach (var series in chartComparison.Series)
                {
                    series.ChartArea = "MainArea";
                }

                // 应用Y轴自适应刻度到恢复的图表区域
                AdjustYAxisScale();

                isSeparated = false;
                btnSeparate.Visible = true;
                btnRestore.Visible = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"恢复显示失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入数据按钮事件 - 支持导入Excel、CSV文件和JSON对比数据
        /// </summary>
        private void BtnImportData_Click(object sender, EventArgs e)
        {
            try
            {
                // 首先让用户选择数据类型
                using (var dataTypeForm = new DataTypeSelectionForm())
                {
                    if (dataTypeForm.ShowDialog() != DialogResult.OK)
                    {
                        return; // 用户取消选择
                    }

                    var selectedDataType = dataTypeForm.SelectedDataType;

                    // 根据数据类型设置文件过滤器
                    OpenFileDialog openDialog = new OpenFileDialog();
                    if (selectedDataType == DataTypeSelectionForm.DataType.MineralComposition)
                    {
                        openDialog.Filter = "矿物组分数据文件|*.xlsx;*.xls;*.csv|Excel文件|*.xlsx;*.xls|CSV文件|*.csv";
                        openDialog.Title = "选择矿物组分法数据文件";
                    }
                    else
                    {
                        openDialog.Filter = "岩石力学数据文件|*.xlsx;*.xls;*.csv|Excel文件|*.xlsx;*.xls|CSV文件|*.csv";
                        openDialog.Title = "选择煤储层静态岩石力学参数法数据文件";
                    }

                    if (openDialog.ShowDialog() == DialogResult.OK)
                    {
                        string filePath = openDialog.FileName;
                        string extension = Path.GetExtension(filePath).ToLower();

                        switch (extension)
                        {
                            case ".xlsx":
                            case ".xls":
                                ImportExcelData(filePath, selectedDataType);
                                break;
                            case ".csv":
                                ImportCsvData(filePath, selectedDataType);
                                break;
                            default:
                                MessageBox.Show("不支持的文件格式！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入数据失败: {ex.Message}");
                MessageBox.Show($"导入数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入Excel数据
        /// </summary>
        private void ImportExcelData(string filePath, DataTypeSelectionForm.DataType dataType)
        {
            try
            {
                var dataSet = ReadExcelSheets(filePath);
                if (dataSet != null && dataSet.Tables.Count > 0)
                {
                    var dataTable = dataSet.Tables[0];

                    // 根据数据类型设置系列名称和颜色
                    string seriesName;
                    Color seriesColor;
                    if (dataType == DataTypeSelectionForm.DataType.MineralComposition)
                    {
                        seriesName = $"矿物组分法-{Path.GetFileNameWithoutExtension(filePath)}";
                        seriesColor = Color.Blue;
                    }
                    else
                    {
                        seriesName = $"煤储层静态岩石力学参数法-{Path.GetFileNameWithoutExtension(filePath)}";
                        seriesColor = Color.Cyan;
                    }

                    // 检测数据格式并转换为对比数据
                    var comparisonData = ConvertDataTableToComparisonData(dataTable, seriesName, dataType);

                    if (comparisonData != null)
                    {
                        // 添加到图表
                        AddComparisonDataToChart(comparisonData, seriesColor, seriesName, dataType);

                        // 自动调整Y轴刻度
                        AdjustYAxisScale();

                        MessageBox.Show($"成功导入Excel数据，共 {comparisonData.DataPoints.Count} 个数据点", "导入成功",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入Excel数据失败: {ex.Message}");
                MessageBox.Show($"导入Excel数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入CSV数据
        /// </summary>
        private void ImportCsvData(string filePath, DataTypeSelectionForm.DataType dataType)
        {
            try
            {
                var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                if (lines.Length < 2)
                {
                    MessageBox.Show("CSV文件格式不正确或没有数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                var dataPoints = new List<dynamic>();

                // 跳过标题行，解析数据
                for (int i = 1; i < lines.Length; i++)
                {
                    var values = lines[i].Split(',');
                    if (values.Length >= 3)
                    {
                        try
                        {
                            double depth = Convert.ToDouble(values[0].Trim());
                            double brittleIndex = Convert.ToDouble(values[2].Trim()); // 假设第三列是脆性指数

                            if (depth > 0 && brittleIndex >= 0 && brittleIndex <= 100)
                            {
                                dataPoints.Add(new
                                {
                                    TopDepth = depth,
                                    BottomDepth = depth,
                                    BrittleIndex = brittleIndex
                                });
                            }
                        }
                        catch
                        {
                            // 跳过无效行
                        }
                    }
                }

                if (dataPoints.Count > 0)
                {
                    // 根据数据类型设置系列名称和颜色
                    string seriesName;
                    Color seriesColor;
                    if (dataType == DataTypeSelectionForm.DataType.MineralComposition)
                    {
                        seriesName = $"矿物组分法-{Path.GetFileNameWithoutExtension(filePath)}";
                        seriesColor = Color.Blue;
                    }
                    else
                    {
                        seriesName = $"煤储层静态岩石力学参数法-{Path.GetFileNameWithoutExtension(filePath)}";
                        seriesColor = Color.Cyan;
                    }

                    var comparisonData = new
                    {
                        SystemName = seriesName,
                        DataPoints = dataPoints,
                        ImportTime = DateTime.Now
                    };

                    AddComparisonDataToChart(comparisonData, seriesColor, seriesName, dataType);

                    // 自动调整Y轴刻度
                    AdjustYAxisScale();

                    MessageBox.Show($"成功导入CSV数据，共 {dataPoints.Count} 个数据点", "导入成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("CSV文件中没有找到有效的数据！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入CSV数据失败: {ex.Message}");
                MessageBox.Show($"导入CSV数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入JSON对比数据
        /// </summary>
        private void ImportJsonData(string filePath)
        {
            try
            {
                string jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                dynamic data = JsonConvert.DeserializeObject(jsonContent);

                if (data?.DataPoints != null)
                {
                    string systemName = data.SystemName?.ToString() ?? Path.GetFileNameWithoutExtension(filePath);
                    AddComparisonDataToChart(data, Color.Purple, $"JSON导入-{systemName}");

                    int pointCount = 0;
                    foreach (var point in data.DataPoints)
                    {
                        pointCount++;
                    }

                    MessageBox.Show($"成功导入JSON对比数据，共 {pointCount} 个数据点", "导入成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("JSON文件格式不正确或没有数据点！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导入JSON数据失败: {ex.Message}");
                MessageBox.Show($"导入JSON数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 读取Excel文件 - 参考原系统实现
        /// </summary>
        private DataSet ReadExcelSheets(string filePath)
        {
            try
            {
                DataSet dataSet = new DataSet();

                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = null;

                    if (Path.GetExtension(filePath).ToLower() == ".xls")
                    {
                        workbook = new HSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(stream);
                    }

                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        ISheet sheet = workbook.GetSheetAt(i);
                        DataTable dataTable = new DataTable(sheet.SheetName);

                        // 读取表头
                        IRow headerRow = sheet.GetRow(0);
                        if (headerRow != null)
                        {
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = headerRow.GetCell(j);
                                string columnName = cell?.ToString() ?? $"Column{j}";
                                dataTable.Columns.Add(columnName);
                            }
                        }

                        // 读取数据行
                        for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow row = sheet.GetRow(rowIndex);
                            if (row != null)
                            {
                                DataRow dataRow = dataTable.NewRow();
                                for (int cellIndex = 0; cellIndex < dataTable.Columns.Count; cellIndex++)
                                {
                                    ICell cell = row.GetCell(cellIndex);
                                    dataRow[cellIndex] = cell?.ToString() ?? "";
                                }
                                dataTable.Rows.Add(dataRow);
                            }
                        }

                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"读取Excel文件失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 将DataTable转换为对比数据格式
        /// </summary>
        private dynamic ConvertDataTableToComparisonData(DataTable dataTable, string systemName, DataTypeSelectionForm.DataType dataType)
        {
            try
            {
                var dataPoints = new List<dynamic>();

                // 智能检测列
                int depthColumnIndex = -1;
                int brittlenessColumnIndex = -1;

                // 查找深度列
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    string columnName = dataTable.Columns[i].ColumnName.ToLower();
                    if (columnName.Contains("深度") || columnName.Contains("depth"))
                    {
                        depthColumnIndex = i;
                        break;
                    }
                }

                // 查找脆性指数列
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    string columnName = dataTable.Columns[i].ColumnName.ToLower();
                    if (columnName.Contains("脆性") || columnName.Contains("brittle") || columnName.Contains("脆性指数"))
                    {
                        brittlenessColumnIndex = i;
                        break;
                    }
                }

                // 如果没有找到特定列，使用默认位置
                if (depthColumnIndex == -1 && dataTable.Columns.Count > 0)
                    depthColumnIndex = 0;
                if (brittlenessColumnIndex == -1 && dataTable.Columns.Count > 2)
                    brittlenessColumnIndex = 2;
                else if (brittlenessColumnIndex == -1 && dataTable.Columns.Count > 1)
                    brittlenessColumnIndex = 1;

                if (depthColumnIndex == -1 || brittlenessColumnIndex == -1)
                {
                    MessageBox.Show("无法识别数据列格式！请确保数据包含深度和脆性指数列。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return null;
                }

                // 转换数据
                foreach (DataRow row in dataTable.Rows)
                {
                    try
                    {
                        double depth = Convert.ToDouble(row[depthColumnIndex]);
                        double brittleIndex = Convert.ToDouble(row[brittlenessColumnIndex]);

                        if (depth > 0 && brittleIndex >= 0 && brittleIndex <= 100)
                        {
                            dataPoints.Add(new
                            {
                                TopDepth = depth,
                                BottomDepth = depth,
                                BrittleIndex = brittleIndex
                            });
                        }
                    }
                    catch
                    {
                        // 跳过无效行
                    }
                }

                return new
                {
                    SystemName = systemName,
                    DataPoints = dataPoints,
                    ImportTime = DateTime.Now
                };
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"转换DataTable失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将对比数据添加到图表中
        /// </summary>
        private void AddComparisonDataToChart(dynamic comparisonData, Color seriesColor, string seriesName, DataTypeSelectionForm.DataType? dataType = null)
        {
            try
            {
                if (comparisonData?.DataPoints == null)
                    return;

                // 确保图表已初始化
                if (chartComparison.ChartAreas.Count == 0)
                {
                    InitializeChart();
                }

                // 创建数据系列 - 根据数据类型设置不同的绘制样式
                Series series = new Series(seriesName);

                // 根据数据类型或系统名称设置不同的绘制样式
                if (dataType == DataTypeSelectionForm.DataType.MineralComposition ||
                    seriesName.Contains("矿物") || seriesName.Contains("Mineral"))
                {
                    // 矿物组分法：使用带数据点的线条 - 参考MineralogicalForm中pnlChart绘制曲线的规格
                    series.ChartType = SeriesChartType.Line;
                    series.Color = seriesColor;
                    series.BorderWidth = 2; // 与原系统MineralogicalForm一致
                    series.MarkerStyle = MarkerStyle.Circle;
                    series.MarkerSize = 8;
                    series.MarkerColor = Color.Red; // 与原系统一致，使用红色数据点
                    series.MarkerBorderColor = Color.White;
                    series.MarkerBorderWidth = 1;
                }
                else if (dataType == DataTypeSelectionForm.DataType.StaticRockMechanics ||
                         seriesName.Contains("静态岩石力学参数法"))
                {
                    // 静态岩石力学参数法：使用平滑曲线，不显示数据点
                    series.ChartType = SeriesChartType.Spline;
                    series.Color = seriesColor;
                    series.BorderWidth = 2;
                    series.MarkerStyle = MarkerStyle.None;
                    series.MarkerSize = 0;
                }
                else
                {
                    // 其他数据：使用普通线条
                    series.ChartType = SeriesChartType.Line;
                    series.Color = seriesColor;
                    series.BorderWidth = 2;
                    series.MarkerStyle = MarkerStyle.None;
                }

                // 添加数据点
                int pointCount = 0;
                foreach (var point in comparisonData.DataPoints)
                {
                    try
                    {
                        if (point?.BrittleIndex != null && point?.TopDepth != null)
                        {
                            double brittleIndex = Convert.ToDouble(point.BrittleIndex);
                            double depth = Convert.ToDouble(point.TopDepth);

                            if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                            {
                                series.Points.AddXY(brittleIndex, depth);
                                pointCount++;
                            }
                        }
                    }
                    catch (Exception pointEx)
                    {
                        LoggingService.Instance.Warning($"处理数据点时出错: {pointEx.Message}");
                    }
                }

                if (pointCount > 0)
                {
                    chartComparison.Series.Add(series);

                    // 更新标题
                    int totalSeries = chartComparison.Series.Count;
                    int totalPoints = chartComparison.Series.Sum(s => s?.Points?.Count ?? 0);
                    lblTitle.Text = $"脆性指数计算方法对比图 - 已加载 {totalSeries} 个系统的数据，共 {totalPoints} 个数据点";

                    // 更新按钮状态
                    if (totalSeries >= 2)
                    {
                        btnSeparate.Visible = true;
                        btnRestore.Visible = false;
                        isSeparated = false;
                    }

                    LoggingService.Instance.Info($"已添加 {seriesName} 数据到图表，共 {pointCount} 个数据点");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"添加对比数据到图表失败: {ex.Message}");
                MessageBox.Show($"添加对比数据到图表失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清除历史数据按钮点击事件
        /// </summary>
        private void BtnClearHistory_Click(object sender, EventArgs e)
        {
            try
            {
                // 确认操作
                var result = MessageBox.Show("确定要清除所有历史数据，只保留当前数据吗？", "确认清除",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 移除所有历史数据系列（名称包含"历史数据"的系列）
                    var seriesToRemove = new List<Series>();
                    foreach (Series series in chartComparison.Series)
                    {
                        if (series.Name.Contains("历史数据"))
                        {
                            seriesToRemove.Add(series);
                        }
                    }

                    foreach (var series in seriesToRemove)
                    {
                        chartComparison.Series.Remove(series);
                        LoggingService.Instance.Info($"已移除历史数据系列: {series.Name}");
                    }

                    // 重新调整Y轴刻度
                    AdjustYAxisScale();

                    // 更新标题
                    int remainingSeries = chartComparison.Series.Count;
                    int remainingPoints = chartComparison.Series.Sum(s => s?.Points?.Count ?? 0);
                    lblTitle.Text = $"脆性指数计算方法对比图 - 当前数据: {remainingSeries}系列, 共 {remainingPoints} 个数据点";

                    // 隐藏清除历史按钮
                    btnClearHistory.Visible = false;

                    // 刷新图表
                    chartComparison.Invalidate();

                    LoggingService.Instance.Info($"已清除 {seriesToRemove.Count} 个历史数据系列，剩余 {remainingSeries} 个当前数据系列");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"清除历史数据失败: {ex.Message}");
                MessageBox.Show($"清除历史数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 历史数据对比按钮点击事件
        /// </summary>
        private void BtnHistoryComparison_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("打开历史数据对比选择窗体");

                // 获取历史数据目录
                string historyDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "EnhancedStaticRockMechanicsSystem", "HistoryData");

                // 检查历史数据目录是否存在
                if (!Directory.Exists(historyDataPath))
                {
                    MessageBox.Show("还没有历史数据！请先保存一些对比图数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取所有历史数据文件
                var historyFiles = Directory.GetFiles(historyDataPath, "History_*.json")
                    .OrderByDescending(f => File.GetCreationTime(f))
                    .ToList();

                if (historyFiles.Count == 0)
                {
                    MessageBox.Show("还没有历史数据！请先保存一些对比图数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 创建历史数据选择窗体
                var historyForm = new HistoryComparisonSelectionForm(historyFiles);

                if (historyForm.ShowDialog() == DialogResult.OK)
                {
                    // 获取选中的历史数据文件
                    var selectedFiles = historyForm.SelectedFiles;

                    if (selectedFiles != null && selectedFiles.Count > 0)
                    {
                        // 不清除当前图表，保留现有数据进行叠加对比
                        LoggingService.Instance.Info($"开始添加 {selectedFiles.Count} 个历史数据到当前图表，当前已有 {chartComparison.Series.Count} 个系列");

                        // 为历史数据使用不同的颜色系列（避免与当前数据冲突）
                        var historyColors = new Color[]
                        {
                            Color.Orange, Color.LimeGreen, Color.Magenta,
                            Color.Yellow, Color.Pink, Color.LightCoral,
                            Color.LightBlue, Color.Violet, Color.Gold
                        };

                        int colorIndex = 0;
                        foreach (string filePath in selectedFiles)
                        {
                            try
                            {
                                // 读取历史数据文件
                                string jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                                var historyData = JsonConvert.DeserializeObject<dynamic>(jsonContent);

                                if (historyData?.DataPoints != null)
                                {
                                    // 创建详细的历史数据系列名称
                                    string dataSource = historyData.DataSource?.ToString() ?? "未知数据源";
                                    DateTime saveTime = Convert.ToDateTime(historyData.SaveTime);
                                    int historyPoints = historyData.TotalPoints ?? 0;
                                    string seriesName = $"历史数据: {dataSource} ({saveTime:MM-dd HH:mm}, {historyPoints}点)";

                                    // 创建历史数据系列 - 使用静态岩石力学参数法的绘制方法
                                    var series = new Series(seriesName)
                                    {
                                        ChartType = SeriesChartType.Spline, // 使用平滑曲线，与静态岩石力学参数法一致
                                        Color = historyColors[colorIndex % historyColors.Length],
                                        BorderWidth = 2,
                                        MarkerStyle = MarkerStyle.None, // 不显示数据点，与静态岩石力学参数法一致
                                        MarkerSize = 0,
                                        IsVisibleInLegend = true
                                    };

                                    LoggingService.Instance.Info($"创建历史数据系列: {seriesName}, 颜色: {series.Color.Name}");

                                    // 添加历史数据点
                                    int validPoints = 0;
                                    foreach (var point in historyData.DataPoints)
                                    {
                                        try
                                        {
                                            double depth = Convert.ToDouble(point.TopDepth);
                                            double brittleIndex = Convert.ToDouble(point.BrittleIndex);

                                            // 确保数据有效
                                            if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                                            {
                                                series.Points.AddXY(brittleIndex, depth);
                                                validPoints++;
                                            }
                                        }
                                        catch (Exception pointEx)
                                        {
                                            LoggingService.Instance.Warning($"处理历史数据点时出错: {pointEx.Message}");
                                        }
                                    }

                                    if (validPoints > 0)
                                    {
                                        chartComparison.Series.Add(series);
                                        LoggingService.Instance.Info($"已添加历史数据系列: {seriesName}, 有效数据点: {validPoints}");
                                    }

                                    colorIndex++;
                                }
                            }
                            catch (Exception ex)
                            {
                                LoggingService.Instance.Warning($"加载历史数据失败 {filePath}: {ex.Message}");
                            }
                        }

                        // 自动调整Y轴刻度（关键修复）
                        AdjustYAxisScale();

                        // 更新标题 - 区分当前数据和历史数据
                        int totalSeries = chartComparison.Series.Count;
                        int totalPoints = chartComparison.Series.Sum(s => s?.Points?.Count ?? 0);
                        int historySeries = selectedFiles.Count;
                        int currentSeries = totalSeries - historySeries;

                        lblTitle.Text = $"数据对比图 - 当前数据: {currentSeries}系列, 历史数据: {historySeries}系列, 共 {totalPoints} 个数据点";

                        // 显示清除历史数据按钮
                        btnClearHistory.Visible = true;

                        // 激活分割和恢复按钮（如果有多个系列）
                        if (totalSeries >= 2)
                        {
                            btnSeparate.Visible = true;
                            btnRestore.Visible = false;
                            isSeparated = false;
                            LoggingService.Instance.Info($"已激活分割显示功能，总系列数: {totalSeries}");
                        }

                        // 刷新图表
                        chartComparison.Invalidate();

                        LoggingService.Instance.Info($"历史数据叠加对比完成，当前系列: {currentSeries}, 新增历史系列: {historySeries}, 总系列: {totalSeries}");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"历史数据对比失败: {ex.Message}");
                MessageBox.Show($"历史数据对比失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 自动调整Y轴刻度 - 与原系统ComparisonChartForm完全一致
        /// </summary>
        private void AdjustYAxisScale()
        {
            try
            {
                if (chartComparison?.Series == null || chartComparison.Series.Count == 0)
                {
                    LoggingService.Instance.Warning("无法调整Y轴刻度：没有数据系列");
                    return;
                }

                if (chartComparison.ChartAreas == null || chartComparison.ChartAreas.Count == 0)
                {
                    LoggingService.Instance.Warning("无法调整Y轴刻度：没有图表区域");
                    return;
                }

                // 计算所有系列的深度范围
                double minDepth = double.MaxValue;
                double maxDepth = double.MinValue;

                foreach (var series in chartComparison.Series)
                {
                    if (series?.Points != null)
                    {
                        foreach (var point in series.Points)
                        {
                            double depth = point.YValues[0]; // Y轴是深度
                            if (depth > 0) // 确保深度有效
                            {
                                minDepth = Math.Min(minDepth, depth);
                                maxDepth = Math.Max(maxDepth, depth);
                            }
                        }
                    }
                }

                // 设置合适的深度范围，添加一些边距
                if (minDepth != double.MaxValue && maxDepth != double.MinValue)
                {
                    double depthRange = maxDepth - minDepth;
                    double margin = depthRange * 0.1; // 10%边距

                    chartComparison.ChartAreas[0].AxisY.Minimum = Math.Max(0, minDepth - margin);
                    chartComparison.ChartAreas[0].AxisY.Maximum = maxDepth + margin;

                    // 设置合适的间隔
                    double interval = depthRange / 10;
                    if (interval > 0)
                    {
                        // 对于小范围数据（如0.1米变化），使用更精细的间隔
                        if (depthRange < 1.0)
                        {
                            interval = Math.Max(0.01, Math.Round(interval, 2)); // 最小0.01米间隔
                        }
                        else if (depthRange < 10.0)
                        {
                            interval = Math.Max(0.1, Math.Round(interval, 1)); // 最小0.1米间隔
                        }
                        else
                        {
                            interval = Math.Max(1, Math.Round(interval)); // 最小1米间隔
                        }

                        chartComparison.ChartAreas[0].AxisY.Interval = interval;
                    }

                    chartComparison.ChartAreas[0].RecalculateAxesScale();

                    LoggingService.Instance.Info($"Y轴刻度已调整: 深度范围 {minDepth:F2}-{maxDepth:F2}m, 间隔 {interval:F2}m");
                }
                else
                {
                    LoggingService.Instance.Warning("无法调整Y轴刻度：没有有效的深度数据");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"调整Y轴刻度失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为所有图表区域调整Y轴刻度（用于分割显示模式）
        /// </summary>
        private void AdjustYAxisScaleForAllAreas()
        {
            try
            {
                if (chartComparison?.Series == null || chartComparison.Series.Count == 0)
                {
                    LoggingService.Instance.Warning("无法调整Y轴刻度：没有数据系列");
                    return;
                }

                if (chartComparison.ChartAreas == null || chartComparison.ChartAreas.Count == 0)
                {
                    LoggingService.Instance.Warning("无法调整Y轴刻度：没有图表区域");
                    return;
                }

                // 计算所有系列的深度范围
                double minDepth = double.MaxValue;
                double maxDepth = double.MinValue;

                foreach (var series in chartComparison.Series)
                {
                    if (series?.Points != null)
                    {
                        foreach (var point in series.Points)
                        {
                            double depth = point.YValues[0]; // Y轴是深度
                            if (depth > 0) // 确保深度有效
                            {
                                minDepth = Math.Min(minDepth, depth);
                                maxDepth = Math.Max(maxDepth, depth);
                            }
                        }
                    }
                }

                // 为所有图表区域设置相同的Y轴刻度
                if (minDepth != double.MaxValue && maxDepth != double.MinValue)
                {
                    double depthRange = maxDepth - minDepth;
                    double margin = depthRange * 0.1; // 10%边距

                    double yMin = Math.Max(0, minDepth - margin);
                    double yMax = maxDepth + margin;

                    // 设置合适的间隔
                    double interval = depthRange / 10;
                    if (interval > 0)
                    {
                        // 对于小范围数据（如0.1米变化），使用更精细的间隔
                        if (depthRange < 1.0)
                        {
                            interval = Math.Max(0.01, Math.Round(interval, 2)); // 最小0.01米间隔
                        }
                        else if (depthRange < 10.0)
                        {
                            interval = Math.Max(0.1, Math.Round(interval, 1)); // 最小0.1米间隔
                        }
                        else
                        {
                            interval = Math.Max(1, Math.Round(interval)); // 最小1米间隔
                        }
                    }

                    // 应用到所有图表区域
                    foreach (var area in chartComparison.ChartAreas)
                    {
                        area.AxisY.Minimum = yMin;
                        area.AxisY.Maximum = yMax;
                        area.AxisY.Interval = interval;
                        area.RecalculateAxesScale();
                    }

                    LoggingService.Instance.Info($"所有图表区域Y轴刻度已调整: 深度范围 {minDepth:F2}-{maxDepth:F2}m, 间隔 {interval:F2}m");
                }
                else
                {
                    LoggingService.Instance.Warning("无法调整Y轴刻度：没有有效的深度数据");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"调整所有图表区域Y轴刻度失败: {ex.Message}");
            }
        }
    }
}
