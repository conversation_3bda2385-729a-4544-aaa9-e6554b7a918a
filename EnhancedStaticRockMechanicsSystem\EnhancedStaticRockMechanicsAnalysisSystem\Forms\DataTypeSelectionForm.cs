using System;
using System.Drawing;
using System.Windows.Forms;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    /// <summary>
    /// 数据类型选择对话框
    /// </summary>
    public partial class DataTypeSelectionForm : Form
    {
        public enum DataType
        {
            MineralComposition,  // 矿物组分法
            StaticRockMechanics  // 煤系气储层静态岩石力学参数法
        }

        public DataType SelectedDataType { get; private set; }

        private Button btnMineralComposition;
        private Button btnStaticRockMechanics;
        private Button btnCancel;
        private Label lblTitle;
        private Label lblDescription;
        private Panel pnlButtons;

        public DataTypeSelectionForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // 窗体设置
            this.Text = "选择数据类型";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(33, 33, 33);
            this.ForeColor = Color.White;

            // 标题标签
            lblTitle = new Label();
            lblTitle.Text = "请选择要导入的数据类型";
            lblTitle.Font = new Font("Microsoft YaHei", 14F, FontStyle.Bold);
            lblTitle.ForeColor = Color.LightSkyBlue;
            lblTitle.AutoSize = true;
            lblTitle.Location = new Point(50, 30);
            this.Controls.Add(lblTitle);

            // 描述标签
            lblDescription = new Label();
            lblDescription.Text = "不同的数据类型将使用不同的曲线绘制方法：\n\n" +
                                 "• 矿物组分法：使用Line类型，显示数据点标记\n" +
                                 "• 煤系气储层静态岩石力学参数法：使用Spline类型，平滑曲线";
            lblDescription.Font = new Font("Microsoft YaHei", 10F);
            lblDescription.ForeColor = Color.LightGray;
            lblDescription.Size = new Size(400, 80);
            lblDescription.Location = new Point(50, 70);
            this.Controls.Add(lblDescription);

            // 按钮面板
            pnlButtons = new Panel();
            pnlButtons.Size = new Size(400, 120);
            pnlButtons.Location = new Point(50, 160);
            pnlButtons.BackColor = Color.Transparent;
            this.Controls.Add(pnlButtons);

            // 矿物组分法按钮
            btnMineralComposition = new Button();
            btnMineralComposition.Text = "矿物组分法数据";
            btnMineralComposition.Size = new Size(180, 50);
            btnMineralComposition.Location = new Point(10, 10);
            btnMineralComposition.BackColor = Color.FromArgb(50, 50, 50);
            btnMineralComposition.ForeColor = Color.LightSkyBlue;
            btnMineralComposition.FlatStyle = FlatStyle.Flat;
            btnMineralComposition.FlatAppearance.BorderColor = Color.Cyan;
            btnMineralComposition.Font = new Font("Microsoft YaHei", 10F);
            btnMineralComposition.Click += BtnMineralComposition_Click;
            pnlButtons.Controls.Add(btnMineralComposition);

            // 煤系气储层静态岩石力学参数法按钮
            btnStaticRockMechanics = new Button();
            btnStaticRockMechanics.Text = "煤系气储层静态岩石力学参数法数据";
            btnStaticRockMechanics.Size = new Size(180, 50);
            btnStaticRockMechanics.Location = new Point(210, 10);
            btnStaticRockMechanics.BackColor = Color.FromArgb(50, 50, 50);
            btnStaticRockMechanics.ForeColor = Color.LightSkyBlue;
            btnStaticRockMechanics.FlatStyle = FlatStyle.Flat;
            btnStaticRockMechanics.FlatAppearance.BorderColor = Color.Cyan;
            btnStaticRockMechanics.Font = new Font("Microsoft YaHei", 10F);
            btnStaticRockMechanics.Click += BtnStaticRockMechanics_Click;
            pnlButtons.Controls.Add(btnStaticRockMechanics);

            // 取消按钮
            btnCancel = new Button();
            btnCancel.Text = "取消";
            btnCancel.Size = new Size(100, 40);
            btnCancel.Location = new Point(150, 70);
            btnCancel.BackColor = Color.FromArgb(60, 60, 60);
            btnCancel.ForeColor = Color.White;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.FlatAppearance.BorderColor = Color.Gray;
            btnCancel.Font = new Font("Microsoft YaHei", 9F);
            btnCancel.Click += BtnCancel_Click;
            pnlButtons.Controls.Add(btnCancel);

            // 添加鼠标悬停效果
            AddHoverEffects();
        }

        private void AddHoverEffects()
        {
            // 矿物组分法按钮悬停效果
            btnMineralComposition.MouseEnter += (s, e) =>
            {
                btnMineralComposition.BackColor = Color.FromArgb(70, 70, 70);
                btnMineralComposition.FlatAppearance.BorderColor = Color.LightCyan;
            };
            btnMineralComposition.MouseLeave += (s, e) =>
            {
                btnMineralComposition.BackColor = Color.FromArgb(50, 50, 50);
                btnMineralComposition.FlatAppearance.BorderColor = Color.Cyan;
            };

            // 煤系气储层静态岩石力学参数法按钮悬停效果
            btnStaticRockMechanics.MouseEnter += (s, e) =>
            {
                btnStaticRockMechanics.BackColor = Color.FromArgb(70, 70, 70);
                btnStaticRockMechanics.FlatAppearance.BorderColor = Color.LightCyan;
            };
            btnStaticRockMechanics.MouseLeave += (s, e) =>
            {
                btnStaticRockMechanics.BackColor = Color.FromArgb(50, 50, 50);
                btnStaticRockMechanics.FlatAppearance.BorderColor = Color.Cyan;
            };

            // 取消按钮悬停效果
            btnCancel.MouseEnter += (s, e) =>
            {
                btnCancel.BackColor = Color.FromArgb(80, 80, 80);
                btnCancel.FlatAppearance.BorderColor = Color.LightGray;
            };
            btnCancel.MouseLeave += (s, e) =>
            {
                btnCancel.BackColor = Color.FromArgb(60, 60, 60);
                btnCancel.FlatAppearance.BorderColor = Color.Gray;
            };
        }

        private void BtnMineralComposition_Click(object sender, EventArgs e)
        {
            SelectedDataType = DataType.MineralComposition;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnStaticRockMechanics_Click(object sender, EventArgs e)
        {
            SelectedDataType = DataType.StaticRockMechanics;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
