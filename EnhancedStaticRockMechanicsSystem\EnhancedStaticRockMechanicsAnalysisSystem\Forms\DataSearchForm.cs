using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    /// <summary>
    /// 数据搜索窗体 - 参考BritSystem中MineralogicalForm的搜索功能
    /// </summary>
    public partial class DataSearchForm : Form
    {
        private ComboBox cboSearchColumn;
        private TextBox txtMinValue;
        private TextBox txtMaxValue;
        private Button btnSearch;
        private Button btnResetData;
        private Label lblSearchTitle;
        private Label lblSearchColumn;
        private Label lblMinValue;
        private Label lblMaxValue;

        private DataTable originalData;
        private DataTable currentData;
        private StaticRockMechanicsForm parentForm;

        public event EventHandler<DataFilterEventArgs> DataFiltered;
        public event EventHandler DataReset;

        public DataSearchForm(StaticRockMechanicsForm parent, DataTable data)
        {
            parentForm = parent;
            originalData = data?.Copy();
            currentData = data;
            InitializeComponent();
            SetupTransparency();
            UpdateSearchColumnComboBox();
        }

        private void InitializeComponent()
        {
            cboSearchColumn = new ComboBox();
            txtMinValue = new TextBox();
            txtMaxValue = new TextBox();
            btnSearch = new Button();
            btnResetData = new Button();
            lblSearchTitle = new Label();
            lblSearchColumn = new Label();
            lblMinValue = new Label();
            lblMaxValue = new Label();
            SuspendLayout();
            // 
            // cboSearchColumn
            // 
            cboSearchColumn.BackColor = Color.FromArgb(60, 60, 60);
            cboSearchColumn.DropDownStyle = ComboBoxStyle.DropDownList;
            cboSearchColumn.FlatStyle = FlatStyle.Flat;
            cboSearchColumn.Font = new Font("微软雅黑", 9F);
            cboSearchColumn.ForeColor = Color.White;
            cboSearchColumn.Location = new Point(98, 56);
            cboSearchColumn.Name = "cboSearchColumn";
            cboSearchColumn.Size = new Size(120, 32);
            cboSearchColumn.TabIndex = 2;
            // 
            // txtMinValue
            // 
            txtMinValue.BackColor = Color.FromArgb(60, 60, 60);
            txtMinValue.BorderStyle = BorderStyle.FixedSingle;
            txtMinValue.Font = new Font("微软雅黑", 9F);
            txtMinValue.ForeColor = Color.White;
            txtMinValue.Location = new Point(321, 57);
            txtMinValue.Name = "txtMinValue";
            txtMinValue.Size = new Size(80, 31);
            txtMinValue.TabIndex = 4;
            // 
            // txtMaxValue
            // 
            txtMaxValue.BackColor = Color.FromArgb(60, 60, 60);
            txtMaxValue.BorderStyle = BorderStyle.FixedSingle;
            txtMaxValue.Font = new Font("微软雅黑", 9F);
            txtMaxValue.ForeColor = Color.White;
            txtMaxValue.Location = new Point(510, 57);
            txtMaxValue.Name = "txtMaxValue";
            txtMaxValue.Size = new Size(80, 31);
            txtMaxValue.TabIndex = 6;
            // 
            // btnSearch
            // 
            btnSearch.BackColor = Color.FromArgb(40, 167, 69);
            btnSearch.FlatAppearance.BorderColor = Color.LightGreen;
            btnSearch.FlatStyle = FlatStyle.Flat;
            btnSearch.Font = new Font("微软雅黑", 9F);
            btnSearch.ForeColor = Color.White;
            btnSearch.Location = new Point(510, 105);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(80, 35);
            btnSearch.TabIndex = 8;
            btnSearch.Text = "搜索";
            btnSearch.UseVisualStyleBackColor = false;
            btnSearch.Click += BtnSearch_Click;
            // 
            // btnResetData
            // 
            btnResetData.BackColor = Color.FromArgb(108, 117, 125);
            btnResetData.FlatAppearance.BorderColor = Color.Gray;
            btnResetData.FlatStyle = FlatStyle.Flat;
            btnResetData.Font = new Font("微软雅黑", 9F);
            btnResetData.ForeColor = Color.White;
            btnResetData.Location = new Point(387, 105);
            btnResetData.Name = "btnResetData";
            btnResetData.Size = new Size(99, 35);
            btnResetData.TabIndex = 7;
            btnResetData.Text = "还原数据";
            btnResetData.UseVisualStyleBackColor = false;
            btnResetData.Click += BtnResetData_Click;
            // 
            // lblSearchTitle
            // 
            lblSearchTitle.Font = new Font("微软雅黑", 12F, FontStyle.Bold);
            lblSearchTitle.ForeColor = Color.White;
            lblSearchTitle.Location = new Point(10, 10);
            lblSearchTitle.Name = "lblSearchTitle";
            lblSearchTitle.Size = new Size(142, 35);
            lblSearchTitle.TabIndex = 0;
            lblSearchTitle.Text = "数据搜索";
            // 
            // lblSearchColumn
            // 
            lblSearchColumn.Font = new Font("微软雅黑", 9F);
            lblSearchColumn.ForeColor = Color.White;
            lblSearchColumn.Location = new Point(10, 54);
            lblSearchColumn.Name = "lblSearchColumn";
            lblSearchColumn.Size = new Size(82, 34);
            lblSearchColumn.TabIndex = 1;
            lblSearchColumn.Text = "选择列:";
            // 
            // lblMinValue
            // 
            lblMinValue.Font = new Font("微软雅黑", 9F);
            lblMinValue.ForeColor = Color.White;
            lblMinValue.Location = new Point(236, 57);
            lblMinValue.Name = "lblMinValue";
            lblMinValue.Size = new Size(79, 33);
            lblMinValue.TabIndex = 3;
            lblMinValue.Text = "最小值:";
            // 
            // lblMaxValue
            // 
            lblMaxValue.Font = new Font("微软雅黑", 9F);
            lblMaxValue.ForeColor = Color.White;
            lblMaxValue.Location = new Point(416, 59);
            lblMaxValue.Name = "lblMaxValue";
            lblMaxValue.Size = new Size(88, 32);
            lblMaxValue.TabIndex = 5;
            lblMaxValue.Text = "最大值:";
            // 
            // DataSearchForm
            // 
            BackColor = Color.FromArgb(45, 45, 45);
            ClientSize = new Size(638, 182);
            Controls.Add(lblSearchTitle);
            Controls.Add(lblSearchColumn);
            Controls.Add(cboSearchColumn);
            Controls.Add(lblMinValue);
            Controls.Add(txtMinValue);
            Controls.Add(lblMaxValue);
            Controls.Add(txtMaxValue);
            Controls.Add(btnResetData);
            Controls.Add(btnSearch);
            ForeColor = Color.White;
            FormBorderStyle = FormBorderStyle.FixedToolWindow;
            Name = "DataSearchForm";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.Manual;
            Text = "数据搜索";
            TopMost = true;
            ResumeLayout(false);
            PerformLayout();
        }

        private void SetupTransparency()
        {
            // 设置窗体半透明
            this.Opacity = 0.95;
            this.AllowTransparency = true;
        }

        /// <summary>
        /// 更新搜索下拉框中的列名 - 参考原系统实现
        /// </summary>
        private void UpdateSearchColumnComboBox()
        {
            try
            {
                cboSearchColumn.Items.Clear();

                if (currentData == null || currentData.Columns.Count == 0)
                    return;

                // 添加所有数值类型的列名到下拉框
                foreach (DataColumn column in currentData.Columns)
                {
                    // 只添加数值类型的列
                    if (column.DataType == typeof(double) || column.DataType == typeof(float) || 
                        column.DataType == typeof(decimal) || column.DataType == typeof(int))
                    {
                        cboSearchColumn.Items.Add(column.ColumnName);
                    }
                }

                // 如果有列，则默认选择第一列
                if (cboSearchColumn.Items.Count > 0)
                    cboSearchColumn.SelectedIndex = 0;

                LoggingService.Instance.Info($"已更新搜索下拉框，共 {cboSearchColumn.Items.Count} 个数值列");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"更新搜索下拉框时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 搜索按钮点击事件 - 参考原系统实现
        /// </summary>
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                // 验证输入
                if (cboSearchColumn.SelectedItem == null)
                {
                    MessageBox.Show("请选择要搜索的列", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtMinValue.Text) || string.IsNullOrWhiteSpace(txtMaxValue.Text))
                {
                    MessageBox.Show("请输入最小值和最大值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 尝试解析最小值和最大值
                if (!double.TryParse(txtMinValue.Text, out double minValue) || !double.TryParse(txtMaxValue.Text, out double maxValue))
                {
                    MessageBox.Show("请输入有效的数值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确保最小值小于等于最大值
                if (minValue > maxValue)
                {
                    MessageBox.Show("最小值不能大于最大值", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取选择的列名
                string columnName = cboSearchColumn.SelectedItem.ToString();

                // 创建筛选后的数据表
                DataTable filteredData = currentData.Clone();

                // 根据条件筛选数据
                foreach (DataRow row in currentData.Rows)
                {
                    // 跳过空值
                    if (row[columnName] == DBNull.Value)
                        continue;

                    // 尝试将值转换为double
                    if (double.TryParse(row[columnName].ToString(), out double value))
                    {
                        // 如果值在范围内，则添加到筛选后的数据表
                        if (value >= minValue && value <= maxValue)
                        {
                            filteredData.ImportRow(row);
                        }
                    }
                }

                // 如果没有符合条件的数据
                if (filteredData.Rows.Count == 0)
                {
                    MessageBox.Show("没有找到符合条件的数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 触发数据过滤事件
                DataFiltered?.Invoke(this, new DataFilterEventArgs(filteredData, columnName, minValue, maxValue));

                // 显示搜索结果
                MessageBox.Show($"找到 {filteredData.Rows.Count} 条符合条件的数据", "搜索结果", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoggingService.Instance.Info($"数据搜索完成，找到 {filteredData.Rows.Count} 条符合条件的数据");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"搜索数据时出错: {ex.Message}");
                MessageBox.Show($"搜索数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 还原数据按钮点击事件 - 参考原系统实现
        /// </summary>
        private void BtnResetData_Click(object sender, EventArgs e)
        {
            try
            {
                // 如果原始数据为空，则返回
                if (originalData == null || originalData.Rows.Count == 0)
                {
                    MessageBox.Show("没有原始数据可还原", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 触发数据重置事件
                DataReset?.Invoke(this, EventArgs.Empty);

                // 清空搜索条件
                txtMinValue.Text = "";
                txtMaxValue.Text = "";

                // 显示还原成功消息
                MessageBox.Show("已还原原始数据", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoggingService.Instance.Info("数据已还原到原始状态");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"还原数据时出错: {ex.Message}");
                MessageBox.Show($"还原数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新当前数据
        /// </summary>
        public void UpdateCurrentData(DataTable data)
        {
            currentData = data;
            if (originalData == null)
                originalData = data?.Copy();
            UpdateSearchColumnComboBox();
        }
    }

    /// <summary>
    /// 数据过滤事件参数
    /// </summary>
    public class DataFilterEventArgs : EventArgs
    {
        public DataTable FilteredData { get; }
        public string ColumnName { get; }
        public double MinValue { get; }
        public double MaxValue { get; }

        public DataFilterEventArgs(DataTable filteredData, string columnName, double minValue, double maxValue)
        {
            FilteredData = filteredData;
            ColumnName = columnName;
            MinValue = minValue;
            MaxValue = maxValue;
        }
    }
}
