using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;
using Newtonsoft.Json;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    /// <summary>
    /// 历史数据对比选择窗体
    /// </summary>
    public partial class HistoryComparisonSelectionForm : Form
    {
        private ListView listViewHistory;
        private Button btnOK;
        private Button btnCancel;
        private Button btnDelete;
        private Label lblTitle;
        private Label lblInstruction;
        
        public List<string> SelectedFiles { get; private set; } = new List<string>();
        
        private List<string> historyFiles;

        public HistoryComparisonSelectionForm(List<string> files)
        {
            historyFiles = files;
            InitializeComponent();
            LoadHistoryData();
        }

        private void InitializeComponent()
        {
            this.listViewHistory = new ListView();
            this.btnOK = new Button();
            this.btnCancel = new Button();
            this.btnDelete = new Button();
            this.lblTitle = new Label();
            this.lblInstruction = new Label();
            this.SuspendLayout();

            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new Font("微软雅黑", 14F, FontStyle.Bold);
            this.lblTitle.ForeColor = Color.White;
            this.lblTitle.Location = new Point(20, 20);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new Size(200, 31);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "历史数据对比选择";

            // 
            // lblInstruction
            // 
            this.lblInstruction.AutoSize = true;
            this.lblInstruction.ForeColor = Color.LightGray;
            this.lblInstruction.Location = new Point(20, 60);
            this.lblInstruction.Name = "lblInstruction";
            this.lblInstruction.Size = new Size(400, 17);
            this.lblInstruction.TabIndex = 1;
            this.lblInstruction.Text = "请选择要对比的历史数据（可多选，建议选择2-4个进行对比）：";

            // 
            // listViewHistory
            // 
            this.listViewHistory.BackColor = Color.FromArgb(45, 45, 45);
            this.listViewHistory.ForeColor = Color.White;
            this.listViewHistory.FullRowSelect = true;
            this.listViewHistory.GridLines = true;
            this.listViewHistory.Location = new Point(20, 90);
            this.listViewHistory.Name = "listViewHistory";
            this.listViewHistory.Size = new Size(760, 400);
            this.listViewHistory.TabIndex = 2;
            this.listViewHistory.UseCompatibleStateImageBehavior = false;
            this.listViewHistory.View = View.Details;
            this.listViewHistory.CheckBoxes = true;
            this.listViewHistory.ItemChecked += ListView_ItemChecked;

            // 添加列
            this.listViewHistory.Columns.Add("选择", 50);
            this.listViewHistory.Columns.Add("保存时间", 150);
            this.listViewHistory.Columns.Add("数据源", 200);
            this.listViewHistory.Columns.Add("数据点数", 100);
            this.listViewHistory.Columns.Add("深度范围", 120);
            this.listViewHistory.Columns.Add("脆性指数范围", 130);

            // 
            // btnOK
            // 
            this.btnOK.BackColor = Color.FromArgb(0, 120, 215);
            this.btnOK.FlatStyle = FlatStyle.Flat;
            this.btnOK.ForeColor = Color.White;
            this.btnOK.Location = new Point(520, 510);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new Size(100, 35);
            this.btnOK.TabIndex = 3;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = false;
            this.btnOK.Click += BtnOK_Click;

            // 
            // btnCancel
            // 
            this.btnCancel.BackColor = Color.FromArgb(100, 100, 100);
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.Location = new Point(640, 510);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(100, 35);
            this.btnCancel.TabIndex = 4;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += BtnCancel_Click;

            // 
            // btnDelete
            // 
            this.btnDelete.BackColor = Color.FromArgb(200, 50, 50);
            this.btnDelete.FlatStyle = FlatStyle.Flat;
            this.btnDelete.ForeColor = Color.White;
            this.btnDelete.Location = new Point(20, 510);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new Size(100, 35);
            this.btnDelete.TabIndex = 5;
            this.btnDelete.Text = "删除选中";
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += BtnDelete_Click;

            // 
            // HistoryComparisonSelectionForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 17F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.FromArgb(33, 33, 33);
            this.ClientSize = new Size(800, 570);
            this.Controls.Add(this.btnDelete);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.listViewHistory);
            this.Controls.Add(this.lblInstruction);
            this.Controls.Add(this.lblTitle);
            this.Font = new Font("微软雅黑", 9F);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "HistoryComparisonSelectionForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "历史数据对比选择";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void LoadHistoryData()
        {
            try
            {
                listViewHistory.Items.Clear();

                foreach (string filePath in historyFiles)
                {
                    try
                    {
                        string jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                        dynamic historyData = JsonConvert.DeserializeObject(jsonContent);

                        if (historyData != null)
                        {
                            var item = new ListViewItem();
                            item.Text = ""; // 复选框列
                            item.SubItems.Add(Convert.ToDateTime(historyData.SaveTime).ToString("yyyy-MM-dd HH:mm:ss"));
                            item.SubItems.Add(historyData.DataSource?.ToString() ?? "未知");
                            item.SubItems.Add(historyData.TotalPoints?.ToString() ?? "0");

                            // 计算深度和脆性指数范围
                            if (historyData.DataPoints != null)
                            {
                                var depths = new List<double>();
                                var brittleIndexes = new List<double>();

                                foreach (var point in historyData.DataPoints)
                                {
                                    if (point.TopDepth != null)
                                        depths.Add(Convert.ToDouble(point.TopDepth));
                                    if (point.BrittleIndex != null)
                                        brittleIndexes.Add(Convert.ToDouble(point.BrittleIndex));
                                }

                                string depthRange = depths.Count > 0 ? 
                                    $"{depths.Min():F1}-{depths.Max():F1}m" : "无数据";
                                string brittleRange = brittleIndexes.Count > 0 ? 
                                    $"{brittleIndexes.Min():F1}-{brittleIndexes.Max():F1}%" : "无数据";

                                item.SubItems.Add(depthRange);
                                item.SubItems.Add(brittleRange);
                            }
                            else
                            {
                                item.SubItems.Add("无数据");
                                item.SubItems.Add("无数据");
                            }

                            item.Tag = filePath;
                            listViewHistory.Items.Add(item);
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"加载历史文件失败 {filePath}: {ex.Message}");
                    }
                }

                LoggingService.Instance.Info($"已加载 {listViewHistory.Items.Count} 个历史数据记录");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载历史数据失败: {ex.Message}");
                MessageBox.Show($"加载历史数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ListView_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            // 限制最多选择5个项目
            var checkedItems = listViewHistory.CheckedItems;
            if (checkedItems.Count > 5)
            {
                e.Item.Checked = false;
                MessageBox.Show("最多只能选择5个历史数据进行对比！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            var checkedItems = listViewHistory.CheckedItems;
            if (checkedItems.Count == 0)
            {
                MessageBox.Show("请至少选择一个历史数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            SelectedFiles.Clear();
            foreach (ListViewItem item in checkedItems)
            {
                SelectedFiles.Add(item.Tag.ToString());
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            var checkedItems = listViewHistory.CheckedItems;
            if (checkedItems.Count == 0)
            {
                MessageBox.Show("请选择要删除的历史数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show($"确定要删除选中的 {checkedItems.Count} 个历史数据吗？此操作不可撤销！", 
                "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    var itemsToRemove = new List<ListViewItem>();
                    foreach (ListViewItem item in checkedItems)
                    {
                        string filePath = item.Tag.ToString();
                        File.Delete(filePath);
                        itemsToRemove.Add(item);
                        historyFiles.Remove(filePath);
                    }

                    foreach (var item in itemsToRemove)
                    {
                        listViewHistory.Items.Remove(item);
                    }

                    MessageBox.Show($"已删除 {itemsToRemove.Count} 个历史数据！", "删除成功", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    LoggingService.Instance.Error($"删除历史数据失败: {ex.Message}");
                    MessageBox.Show($"删除历史数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
