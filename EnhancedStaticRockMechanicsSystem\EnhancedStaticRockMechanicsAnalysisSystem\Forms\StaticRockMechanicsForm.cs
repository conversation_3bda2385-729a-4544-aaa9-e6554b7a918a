using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Core;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;
using Newtonsoft.Json;
using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    public partial class StaticRockMechanicsForm : Form
    {
        private DataTable mechanicsData;
        private List<RockMechanicsDataPoint> dataPoints;
        private RockMechanicsCalculator calculator;
        private DataTable? originalMechanicsData;
        private string? currentExcelFile;

        // 图表交互相关字段 - 参考原系统
        private List<InternalDataPoint> chartDataPoints = new List<InternalDataPoint>();
        private HashSet<int> selectedRows = new HashSet<int>();

        // 缩放相关字段 - 参考MineralogicalForm的高级缩放实现
        private double currentZoom = 1.0;
        private double currentXZoom = 1.0;
        private const double ZOOM_FACTOR = 1.2;
        private const double MAX_ZOOM = 15.0;
        private const double MAX_X_ZOOM = 3.0;
        private const double MIN_ZOOM = 1.0;

        // 数据搜索相关字段
        private DataSearchForm dataSearchForm;

        // 右键菜单相关字段
        private ContextMenuStrip contextMenuStrip;

        // 自适应缩放相关字段
        private readonly Size originalFormSize = new Size(1400, 900);
        private readonly Dictionary<Control, Rectangle> originalControlBounds = new Dictionary<Control, Rectangle>();
        private readonly Dictionary<Control, Font> originalControlFonts = new Dictionary<Control, Font>();

        // 对比数据管理器 - 暂时注释掉，使用简化版本
        // private UnifiedComparisonDataManager comparisonDataManager;

        // 历史数据存储路径 - 与SimpleComparisonChartForm保持一致
        private readonly string historyDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "EnhancedStaticRockMechanicsSystem", "HistoryData");

        /// <summary>
        /// 内部数据点结构 - 用于图表交互
        /// </summary>
        private struct InternalDataPoint
        {
            public double Depth { get; set; }
            public double BrittlenessIndex { get; set; }
            public int RowIndex { get; set; }
        }

        // 列名识别字典 - 完全参考原系统BritSystem
        private readonly Dictionary<string, List<string>> columnPatterns = new Dictionary<string, List<string>>
        {
            ["深度"] = new List<string> { "深度", "depth", "depths", "顶深", "井深", "md", "tvd", "顶深/m", "深度/m", "深度(m)" },
            ["密度"] = new List<string> { "密度", "ρ", "rho", "rhob", "density", "den", "密度/(g/cm³)", "密度(g/cm³)", "岩石密度" },
            ["纵波速度"] = new List<string> { "纵波速度", "vp", "纵波", "p波", "dt", "纵波时差", "纵波速度/(m/s)", "纵波速度(m/s)", "vp(m/s)" },
            ["横波速度"] = new List<string> { "横波速度", "vs", "横波", "s波", "dts", "横波时差", "横波速度/(m/s)", "横波速度(m/s)", "vs(m/s)" }
        };

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeData();
            // InitializeScaling(); // 暂时禁用自适应缩放，使用设计器布局
            // InitializeComparisonManager(); // 暂时注释掉
            BindUnitSelectionEvents();
            UpdateParameterLabels();
            InitializeContextMenu(); // 初始化右键菜单

            // 绑定Load事件来确保图表在窗体完全加载后初始化
            this.Load += StaticRockMechanicsForm_Load;
        }

        private void StaticRockMechanicsForm_Load(object sender, EventArgs e)
        {
            try
            {
                // 确保图表控件已正确初始化
                if (chartBrittleness == null)
                {
                    LoggingService.Instance.Error("图表控件未初始化，尝试手动创建");
                    MessageBox.Show("图表控件未初始化，请重新启动程序！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 初始化图表设置
                InitializeChart();

                LoggingService.Instance.Info("静态岩石力学参数分析窗体加载完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"窗体加载时出错: {ex.Message}");
                MessageBox.Show($"窗体加载时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化图表设置 - 参考原系统
        /// </summary>
        private void InitializeChart()
        {
            try
            {
                if (chartBrittleness == null)
                {
                    LoggingService.Instance.Error("图表控件为空，无法初始化");
                    return;
                }

                // 清除现有的Series和ChartAreas
                chartBrittleness.Series.Clear();
                chartBrittleness.ChartAreas.Clear();

                // 创建图表区域
                var chartArea = new System.Windows.Forms.DataVisualization.Charting.ChartArea("MainArea");
                chartArea.BackColor = Color.FromArgb(45, 45, 45);
                chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                chartArea.AxisX.LineColor = Color.Gray;
                chartArea.AxisY.LineColor = Color.Gray;
                chartArea.AxisX.MajorGrid.LineColor = Color.DarkGray;
                chartArea.AxisY.MajorGrid.LineColor = Color.DarkGray;
                chartArea.AxisX.Title = "深度 (m)";
                chartArea.AxisY.Title = "脆性指数 (%)";
                chartArea.AxisX.TitleForeColor = Color.White;
                chartArea.AxisY.TitleForeColor = Color.White;

                chartBrittleness.ChartAreas.Add(chartArea);

                // 创建数据系列
                var series = new System.Windows.Forms.DataVisualization.Charting.Series("BrittlenessIndex");
                series.ChartType = System.Windows.Forms.DataVisualization.Charting.SeriesChartType.Line;
                series.Color = Color.Cyan;
                series.BorderWidth = 2;
                series.MarkerStyle = System.Windows.Forms.DataVisualization.Charting.MarkerStyle.Circle;
                series.MarkerSize = 4;
                series.MarkerColor = Color.Yellow;

                chartBrittleness.Series.Add(series);

                // 设置图表背景
                chartBrittleness.BackColor = Color.FromArgb(45, 45, 45);
                chartBrittleness.BorderlineColor = Color.Gray;

                LoggingService.Instance.Info("图表初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"图表初始化失败: {ex.Message}");
                throw;
            }
        }

        private void InitializeData()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("深度/m", typeof(double)); // 修改为只使用深度列
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("静态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            dataPoints = new List<RockMechanicsDataPoint>();
            calculator = new RockMechanicsCalculator();

            // 绑定数据表到DataGridView
            if (dgvMechanicsData != null)
            {
                dgvMechanicsData.DataSource = mechanicsData;
                dgvMechanicsData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
        }



        // 暂时注释掉，使用简化版本
        // private void InitializeComparisonManager()
        // {
        //     try
        //     {
        //         comparisonDataManager = new UnifiedComparisonDataManager();
        //         LoggingService.Instance.Info("对比数据管理器初始化成功");
        //     }
        //     catch (Exception ex)
        //     {
        //         LoggingService.Instance.Error($"对比数据管理器初始化失败: {ex.Message}");
        //     }
        // }

        private void BindUnitSelectionEvents()
        {
            try
            {
                // 设置默认选择：ρ、DT、DTS
                rbDensityRho.Checked = true;
                rbVelocityDt.Checked = true;
                rbVelocityDts.Checked = true;

                // 绑定密度单位选择事件
                rbDensityRho.CheckedChanged += UnitSelection_CheckedChanged;
                rbDensityRhob.CheckedChanged += UnitSelection_CheckedChanged;

                // 绑定纵波速度单位选择事件
                rbVelocityDt.CheckedChanged += UnitSelection_CheckedChanged;
                rbVelocityVp.CheckedChanged += UnitSelection_CheckedChanged;

                // 绑定横波速度单位选择事件
                rbVelocityDts.CheckedChanged += UnitSelection_CheckedChanged;
                rbVelocityVs.CheckedChanged += UnitSelection_CheckedChanged;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"绑定单位选择事件失败: {ex.Message}");
            }
        }

        private void UnitSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (sender is RadioButton rb && rb.Checked)
            {
                UpdateParameterLabels();
            }
        }

        private void UpdateParameterLabels()
        {
            try
            {
                // 更新密度标签
                if (rbDensityRho.Checked)
                {
                    lblDensity.Text = "密度 ρ (g/cm³):";
                }
                else if (rbDensityRhob.Checked)
                {
                    lblDensity.Text = "密度 RHOB (g/cm³):";
                }

                // 更新纵波速度标签
                if (rbVelocityDt.Checked)
                {
                    lblVp.Text = "纵波时差 DT (μs/m):";
                }
                else if (rbVelocityVp.Checked)
                {
                    lblVp.Text = "纵波速度 Vp (m/s):";
                }

                // 更新横波速度标签
                if (rbVelocityDts.Checked)
                {
                    lblVs.Text = "横波时差 DTS (μs/m):";
                }
                else if (rbVelocityVs.Checked)
                {
                    lblVs.Text = "横波速度 Vs (m/s):";
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"更新参数标签失败: {ex.Message}");
            }
        }

        private void btnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有导入的数据需要批量计算 - 完全参考原系统
                if (mechanicsData != null && mechanicsData.Rows.Count > 0)
                {
                    // 批量计算模式
                    CalculateBatchData();
                }
                else
                {
                    // 单个计算模式
                    CalculateSingleData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"计算脆性指数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 单个数据计算 - 完全参考原系统
        /// </summary>
        private void CalculateSingleData()
        {
            // 获取输入参数 - 完全参考原系统
            if (!double.TryParse(txtDensity.Text, out double inputDensity) ||
                !double.TryParse(txtVp.Text, out double inputVp) ||
                !double.TryParse(txtVs.Text, out double inputVs))
            {
                MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 进行单位转换，统一为标准单位
            double density = ConvertDensityToStandard(inputDensity);
            double vp = ConvertVelocityToStandard(inputVp, true); // true表示纵波
            double vs = ConvertVelocityToStandard(inputVs, false); // false表示横波

            // 根据修正后的公式计算静态岩石力学参数 - 完全参考原系统
            var result = CalculateStaticRockMechanics(density, vp, vs);

            // 对于单个计算，使用固定范围计算脆性指数 - 完全参考原系统
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;
            result.BrittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, EsMin, EsMax, MuSMin, MuSMax);

            // 显示计算结果 - 完全参考原系统
            lblCalculationResult.Text = $"Ed={result.Ed:F3}GPa, μd={result.MuD:F4}, Es={result.Es:F3}GPa, μs={result.MuS:F4}, BRIT={result.BrittlenessIndex:F2}%";

            // 添加到数据表 - 完全参考原系统
            DataRow newRow = mechanicsData.NewRow();
            newRow["深度/m"] = 0.0; // 默认值，用户可以在导入数据时修改
            newRow["密度/(g/cm³)"] = density;
            newRow["纵波速度/(m/s)"] = vp;
            newRow["横波速度/(m/s)"] = vs;
            newRow["动态杨氏模量/GPa"] = result.Ed;
            newRow["动态泊松比"] = result.MuD;
            newRow["静态杨氏模量/GPa"] = result.Es;
            newRow["静态泊松比"] = result.MuS;
            newRow["脆性指数/%"] = result.BrittlenessIndex;

            mechanicsData.Rows.Add(newRow);

            LoggingService.Instance.Info($"计算脆性指数: 密度={density}, Vp={vp}, Vs={vs}, 结果={result.BrittlenessIndex:F3}");
        }

        /// <summary>
        /// 更新图表 - 参考原系统设计，正确绘制脆性指数曲线
        /// </summary>
        private void UpdateChart()
        {
            try
            {
                if (chartBrittleness == null || mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    LoggingService.Instance.Warning("图表控件或数据为空，无法更新图表");
                    return;
                }

                // 清除现有图表
                chartBrittleness.Series.Clear();
                chartBrittleness.ChartAreas.Clear();
                chartBrittleness.Legends.Clear();

                // 创建图表区域 - 参考原系统设置
                ChartArea chartArea = new ChartArea("MainArea");
                chartArea.BackColor = Color.FromArgb(45, 45, 45);
                chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                chartArea.AxisX.LineColor = Color.Gray;
                chartArea.AxisY.LineColor = Color.Gray;
                chartArea.AxisX.MajorGrid.LineColor = Color.DarkGray;
                chartArea.AxisY.MajorGrid.LineColor = Color.DarkGray;
                chartArea.AxisX.MajorGrid.Enabled = true;
                chartArea.AxisY.MajorGrid.Enabled = true;

                // 设置轴标题和颜色
                chartArea.AxisX.Title = "脆性指数 (%)";
                chartArea.AxisY.Title = "深度 (m)";
                chartArea.AxisX.TitleForeColor = Color.White;
                chartArea.AxisY.TitleForeColor = Color.White;
                chartArea.AxisY.IsReversed = true; // 深度轴反转，深度小的在上面

                // 启用缩放和滚动功能 - 参考MineralogicalForm的高级配置
                chartArea.CursorX.IsUserEnabled = true;
                chartArea.CursorX.IsUserSelectionEnabled = true;
                chartArea.CursorY.IsUserEnabled = true;
                chartArea.CursorY.IsUserSelectionEnabled = true;
                chartArea.AxisX.ScaleView.Zoomable = true;
                chartArea.AxisY.ScaleView.Zoomable = true;

                // 设置滚动条 - 参考MineralogicalForm
                chartArea.AxisY.ScrollBar.Enabled = true;
                chartArea.AxisX.ScrollBar.Enabled = true;
                chartArea.AxisY.ScrollBar.IsPositionedInside = false; // 将滚动条放在外部
                chartArea.AxisX.ScrollBar.IsPositionedInside = false;
                chartArea.AxisY.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                chartArea.AxisX.ScrollBar.BackColor = Color.FromArgb(60, 60, 60);
                chartArea.AxisY.ScrollBar.ButtonColor = Color.FromArgb(100, 100, 100);
                chartArea.AxisX.ScrollBar.ButtonColor = Color.FromArgb(100, 100, 100);

                // 重置缩放比例
                currentZoom = 1.0;
                currentXZoom = 1.0;

                chartBrittleness.ChartAreas.Add(chartArea);

                // 创建脆性指数曲线系列 - 参考原系统
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.Spline, // 使用平滑曲线
                    Color = Color.Cyan,
                    BorderWidth = 2,
                    MarkerStyle = MarkerStyle.Circle, // 显示数据点
                    MarkerSize = 4,
                    MarkerColor = Color.Yellow
                };

                // 创建高亮点系列（用于显示选中的点）
                var highlightSeries = new Series("高亮点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Red,
                    MarkerStyle = MarkerStyle.Circle,
                    MarkerSize = 8,
                    MarkerBorderColor = Color.Yellow,
                    MarkerBorderWidth = 2
                };

                // 智能识别列名
                string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
                string brittlenessColumnName = "脆性指数/%";

                // 检查深度列
                if (string.IsNullOrEmpty(depthColumnName))
                {
                    LoggingService.Instance.Error("未找到深度列，无法绘制图表");
                    MessageBox.Show("未找到深度列，请检查数据格式！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 检查脆性指数列是否存在
                if (!mechanicsData.Columns.Contains(brittlenessColumnName))
                {
                    LoggingService.Instance.Warning("未找到脆性指数列，无法更新图表");
                    MessageBox.Show("未找到脆性指数列，请先进行计算！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 添加数据点并收集数据点信息 - 参考原系统逻辑
                chartDataPoints.Clear();
                List<double> validDepths = new List<double>();
                List<double> validBrittleness = new List<double>();
                int rowIndex = 0;

                foreach (DataRow row in mechanicsData.Rows)
                {
                    try
                    {
                        // 检查数据是否为空
                        if (row[depthColumnName] == DBNull.Value || row[brittlenessColumnName] == DBNull.Value)
                        {
                            rowIndex++;
                            continue;
                        }

                        double depth = Convert.ToDouble(row[depthColumnName]);
                        double brittleness = Convert.ToDouble(row[brittlenessColumnName]);

                        // 验证数据范围
                        if (brittleness >= 0 && brittleness <= 100 && depth > 0)
                        {
                            // 添加数据点到图表 - X轴是脆性指数，Y轴是深度
                            int pointIndex = series.Points.AddXY(brittleness, depth);

                            // 设置数据点的工具提示
                            series.Points[pointIndex].ToolTip = $"深度: {depth:F2}m, 脆性指数: {brittleness:F2}%";

                            // 保存数据点信息用于交互
                            chartDataPoints.Add(new InternalDataPoint
                            {
                                Depth = depth,
                                BrittlenessIndex = brittleness,
                                RowIndex = rowIndex
                            });

                            validDepths.Add(depth);
                            validBrittleness.Add(brittleness);
                        }
                        else
                        {
                            LoggingService.Instance.Warning($"数据超出范围: 深度={depth}, 脆性指数={brittleness}");
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"处理数据行 {rowIndex} 时出错: {ex.Message}");
                    }
                    rowIndex++;
                }

                // 检查是否有有效数据点
                if (series.Points.Count == 0)
                {
                    LoggingService.Instance.Warning("没有有效的数据点可以绘制");
                    MessageBox.Show("没有有效的数据点可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 添加系列到图表
                chartBrittleness.Series.Add(series);
                chartBrittleness.Series.Add(highlightSeries);

                // 设置轴范围 - 参考原系统
                if (validDepths.Count > 0 && validBrittleness.Count > 0)
                {
                    double minDepth = validDepths.Min() - 5;
                    double maxDepth = validDepths.Max() + 5;
                    double minBrittleness = Math.Max(0, validBrittleness.Min() - 5);
                    double maxBrittleness = Math.Min(100, validBrittleness.Max() + 5);

                    chartArea.AxisY.Minimum = minDepth;
                    chartArea.AxisY.Maximum = maxDepth;
                    chartArea.AxisX.Minimum = minBrittleness;
                    chartArea.AxisX.Maximum = maxBrittleness;
                }

                // 创建图例
                Legend legend = new Legend("MainLegend");
                legend.BackColor = Color.FromArgb(60, 60, 60);
                legend.ForeColor = Color.White;
                legend.Docking = Docking.Top;
                chartBrittleness.Legends.Add(legend);

                LoggingService.Instance.Info($"图表更新完成，共 {chartDataPoints.Count} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"更新图表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化右键菜单 - 添加删除和还原数据功能
        /// </summary>
        private void InitializeContextMenu()
        {
            try
            {
                // 创建右键菜单
                contextMenuStrip = new ContextMenuStrip();
                contextMenuStrip.BackColor = Color.FromArgb(60, 60, 60);
                contextMenuStrip.ForeColor = Color.White;

                // 删除点数据菜单项
                ToolStripMenuItem deleteMenuItem = new ToolStripMenuItem("删除点数据");
                deleteMenuItem.ForeColor = Color.White;
                deleteMenuItem.Click += DeleteDataPoint_Click;
                contextMenuStrip.Items.Add(deleteMenuItem);

                // 分隔线
                contextMenuStrip.Items.Add(new ToolStripSeparator());

                // 还原数据菜单项
                ToolStripMenuItem restoreMenuItem = new ToolStripMenuItem("还原数据");
                restoreMenuItem.ForeColor = Color.White;
                restoreMenuItem.Click += RestoreData_Click;
                contextMenuStrip.Items.Add(restoreMenuItem);

                // 将右键菜单绑定到数据表格
                dgvMechanicsData.ContextMenuStrip = contextMenuStrip;

                LoggingService.Instance.Info("右键菜单初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"初始化右键菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除点数据菜单项点击事件
        /// </summary>
        private void DeleteDataPoint_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有选中的行
                if (dgvMechanicsData.SelectedRows.Count == 0)
                {
                    MessageBox.Show("请先选择要删除的数据行！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 获取选中的行索引
                int selectedRowIndex = dgvMechanicsData.SelectedRows[0].Index;

                // 确认删除对话框
                DialogResult result = MessageBox.Show(
                    $"确定要删除第 {selectedRowIndex + 1} 行数据吗？\n\n此操作将从当前数据集中移除该数据点。",
                    "确认删除",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 保存原始数据（如果还没有保存）
                    if (originalMechanicsData == null)
                    {
                        originalMechanicsData = mechanicsData.Copy();
                    }

                    // 从数据表中删除行
                    mechanicsData.Rows.RemoveAt(selectedRowIndex);

                    // 更新数据表格显示
                    dgvMechanicsData.DataSource = null;
                    dgvMechanicsData.DataSource = mechanicsData;

                    // 重新生成图表数据点
                    chartDataPoints.Clear();
                    GenerateChartDataPointsFromMechanicsData();

                    // 更新图表
                    if (chartBrittleness != null && mechanicsData.Columns.Contains("脆性指数/%"))
                    {
                        UpdateChart();
                    }

                    // 更新搜索窗体的数据（如果已打开）
                    if (dataSearchForm != null && !dataSearchForm.IsDisposed)
                    {
                        dataSearchForm.UpdateCurrentData(mechanicsData);
                    }

                    MessageBox.Show("数据点删除成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoggingService.Instance.Info($"删除数据点成功，行索引: {selectedRowIndex}");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"删除数据点失败: {ex.Message}");
                MessageBox.Show($"删除数据点失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 还原数据菜单项点击事件
        /// </summary>
        private void RestoreData_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有原始数据
                if (originalMechanicsData == null)
                {
                    MessageBox.Show("没有原始数据可还原！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确认还原对话框
                DialogResult result = MessageBox.Show(
                    "确定要还原到原始数据状态吗？\n\n此操作将恢复所有被删除的数据点。",
                    "确认还原",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 还原数据
                    mechanicsData = originalMechanicsData.Copy();

                    // 更新数据表格显示
                    dgvMechanicsData.DataSource = null;
                    dgvMechanicsData.DataSource = mechanicsData;

                    // 重新生成图表数据点
                    chartDataPoints.Clear();
                    GenerateChartDataPointsFromMechanicsData();

                    // 更新图表
                    if (chartBrittleness != null && mechanicsData.Columns.Contains("脆性指数/%"))
                    {
                        UpdateChart();
                    }

                    // 更新搜索窗体的数据（如果已打开）
                    if (dataSearchForm != null && !dataSearchForm.IsDisposed)
                    {
                        dataSearchForm.UpdateCurrentData(mechanicsData);
                    }

                    MessageBox.Show("数据还原成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoggingService.Instance.Info("数据还原成功");
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"还原数据失败: {ex.Message}");
                MessageBox.Show($"还原数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearInputs()
        {
            txtDensity.Clear();
            txtVp.Clear();
            txtVs.Clear();
            txtDensity.Focus();
        }

        /// <summary>
        /// 数据搜索按钮点击事件 - 参考BritSystem中MineralogicalForm的搜索功能
        /// </summary>
        private void btnDataSearch_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("打开数据搜索窗体");

                // 检查是否有数据
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("请先导入数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 如果搜索窗体已存在且未关闭，则激活它
                if (dataSearchForm != null && !dataSearchForm.IsDisposed)
                {
                    dataSearchForm.BringToFront();
                    dataSearchForm.Focus();
                    return;
                }

                // 创建新的搜索窗体
                dataSearchForm = new DataSearchForm(this, mechanicsData);

                // 设置窗体位置（在主窗体右上角）
                dataSearchForm.Location = new Point(
                    this.Location.X + this.Width - dataSearchForm.Width - 20,
                    this.Location.Y + 50
                );

                // 绑定事件
                dataSearchForm.DataFiltered += OnDataFiltered;
                dataSearchForm.DataReset += OnDataReset;

                // 显示搜索窗体
                dataSearchForm.Show(this);

                LoggingService.Instance.Info("数据搜索窗体已打开");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"打开数据搜索窗体失败: {ex.Message}");
                MessageBox.Show($"打开数据搜索窗体失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 数据过滤事件处理 - 参考原系统实现
        /// </summary>
        private void OnDataFiltered(object sender, DataFilterEventArgs e)
        {
            try
            {
                LoggingService.Instance.Info($"应用数据过滤，列: {e.ColumnName}, 范围: {e.MinValue} - {e.MaxValue}");

                // 保存原始数据（如果还没有保存）
                if (originalMechanicsData == null)
                {
                    originalMechanicsData = mechanicsData.Copy();
                }

                // 更新当前数据
                mechanicsData = e.FilteredData;

                // 更新数据网格
                dgvMechanicsData.DataSource = mechanicsData;

                // 重新生成图表数据点
                chartDataPoints.Clear();
                GenerateChartDataPointsFromMechanicsData();

                // 更新图表
                if (chartBrittleness != null && mechanicsData.Columns.Contains("脆性指数/%"))
                {
                    UpdateChart();
                }

                LoggingService.Instance.Info($"数据过滤完成，当前显示 {mechanicsData.Rows.Count} 行数据");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"应用数据过滤失败: {ex.Message}");
                MessageBox.Show($"应用数据过滤失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 数据重置事件处理 - 参考原系统实现
        /// </summary>
        private void OnDataReset(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("重置数据到原始状态");

                // 如果没有原始数据，则返回
                if (originalMechanicsData == null)
                {
                    MessageBox.Show("没有原始数据可还原", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 还原数据
                mechanicsData = originalMechanicsData.Copy();

                // 更新数据网格
                dgvMechanicsData.DataSource = mechanicsData;

                // 重新生成图表数据点
                chartDataPoints.Clear();
                GenerateChartDataPointsFromMechanicsData();

                // 更新图表
                if (chartBrittleness != null && mechanicsData.Columns.Contains("脆性指数/%"))
                {
                    UpdateChart();
                }

                // 更新搜索窗体的数据
                if (dataSearchForm != null && !dataSearchForm.IsDisposed)
                {
                    dataSearchForm.UpdateCurrentData(mechanicsData);
                }

                LoggingService.Instance.Info($"数据已重置，当前显示 {mechanicsData.Rows.Count} 行数据");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"重置数据失败: {ex.Message}");
                MessageBox.Show($"重置数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnImportData_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            var importedData = ds.Tables[0];

                            // 智能识别列名并映射到标准格式 - 完全参考原系统
                            var mappedData = MapImportedDataToStandardFormat(importedData);

                            if (mappedData != null)
                            {
                                mechanicsData = mappedData;
                                originalMechanicsData = mechanicsData.Copy();
                                dgvMechanicsData.DataSource = mechanicsData;

                                // 更新搜索窗体的数据（如果已打开）
                                if (dataSearchForm != null && !dataSearchForm.IsDisposed)
                                {
                                    dataSearchForm.UpdateCurrentData(mechanicsData);
                                }

                                MessageBox.Show($"文件加载成功！共导入 {mechanicsData.Rows.Count} 行数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                LoggingService.Instance.Info($"Excel文件导入成功: {currentExcelFile}，共 {mechanicsData.Rows.Count} 行数据");
                            }
                            else
                            {
                                MessageBox.Show("未能识别Excel文件中的数据列，请检查文件格式！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        LoggingService.Instance.Error($"Excel文件导入失败: {ex.Message}");
                    }
                }
            }
        }

        private void btnExportData_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件|*.xlsx";
                saveFileDialog.Title = "导出数据到Excel文件";
                saveFileDialog.FileName = $"静态岩石力学参数数据_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportExcelFile(saveFileDialog.FileName);
                    MessageBox.Show("数据导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void btnClearData_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据需要清空。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show("确定要清空所有数据吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    mechanicsData.Clear();
                    dataPoints.Clear();
                    dgvMechanicsData.DataSource = null;
                    dgvMechanicsData.DataSource = mechanicsData;

                    MessageBox.Show("数据已清空。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoggingService.Instance.Info("清空所有计算数据");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"清空数据失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 生成曲线按钮点击事件 - 完全参考原系统BritSystem
        /// </summary>
        private void btnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("开始生成脆性指数曲线");

                // 检查数据状态
                LoggingService.Instance.Info($"mechanicsData状态: {(mechanicsData == null ? "null" : $"有 {mechanicsData.Rows.Count} 行数据")}");
                LoggingService.Instance.Info($"chartBrittleness状态: {(chartBrittleness == null ? "null" : "已初始化")}");

                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    LoggingService.Instance.Warning("没有数据可以生成曲线");
                    MessageBox.Show("请先导入数据或进行计算！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查是否有脆性指数数据
                if (!mechanicsData.Columns.Contains("脆性指数/%"))
                {
                    LoggingService.Instance.Warning("缺少脆性指数列");
                    MessageBox.Show("请先计算脆性指数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查图表控件
                if (chartBrittleness == null)
                {
                    LoggingService.Instance.Error("图表控件未初始化");
                    MessageBox.Show("图表控件未初始化，请重新启动程序！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                LoggingService.Instance.Info("开始更新图表");

                // 更新图表
                UpdateChart();

                // 绑定鼠标事件（在UpdateChart中已经绑定，这里确保绑定）
                if (chartBrittleness != null)
                {
                    chartBrittleness.MouseWheel += Chart_MouseWheel;
                    chartBrittleness.MouseClick += Chart_MouseClick;
                    LoggingService.Instance.Info("图表鼠标事件绑定完成");
                }

                // 绑定DataGridView事件
                if (dgvMechanicsData != null)
                {
                    dgvMechanicsData.CellClick += DataGridView_CellClick;
                    LoggingService.Instance.Info("数据表点击事件绑定完成");
                }

                MessageBox.Show("脆性指数曲线已生成！使用鼠标滚轮可以缩放，点击数据表行可以高亮对应点。", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoggingService.Instance.Info("脆性指数曲线生成成功");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"生成曲线失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"生成曲线时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (mechanicsData.Rows.Count > 0)
            {
                DialogResult result = MessageBox.Show("当前有未保存的数据，确定要关闭吗？", "确认关闭",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            LoggingService.Instance.Info("关闭静态岩石力学参数分析窗体");
        }

        // 添加缺失的事件处理方法
        private void btnLogout_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要退出登录吗？", "确认退出",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// 保存曲线按钮点击事件 - 参考BritSystem中MineralogicalForm的实现
        /// </summary>
        private void btnSaveCurve_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查图表是否有数据
                if (chartBrittleness.Series.Count == 0)
                {
                    MessageBox.Show("图表没有数据可以保存！请先生成曲线。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 创建保存文件对话框
                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "PNG图像|*.png|JPEG图像|*.jpg|BMP图像|*.bmp|所有文件|*.*";
                saveDialog.Title = "保存脆性指数曲线图";
                saveDialog.FileName = $"脆性指数曲线_{DateTime.Now:yyyyMMdd_HHmmss}.png";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        // 保存当前缩放状态（如果有的话）
                        var chartArea = chartBrittleness.ChartAreas[0];
                        bool isZoomedY = chartArea.AxisY.ScaleView.IsZoomed;
                        bool isZoomedX = chartArea.AxisX.ScaleView.IsZoomed;
                        double currentMinY = 0, currentMaxY = 0, currentMinX = 0, currentMaxX = 0;

                        if (isZoomedY)
                        {
                            currentMinY = chartArea.AxisY.ScaleView.ViewMinimum;
                            currentMaxY = chartArea.AxisY.ScaleView.ViewMaximum;
                        }
                        if (isZoomedX)
                        {
                            currentMinX = chartArea.AxisX.ScaleView.ViewMinimum;
                            currentMaxX = chartArea.AxisX.ScaleView.ViewMaximum;
                        }

                        // 暂时重置缩放以保存完整图表
                        if (isZoomedY || isZoomedX)
                        {
                            chartArea.AxisY.ScaleView.ZoomReset();
                            chartArea.AxisX.ScaleView.ZoomReset();
                        }

                        // 根据文件扩展名确定保存格式
                        var format = System.Windows.Forms.DataVisualization.Charting.ChartImageFormat.Png;
                        if (saveDialog.FileName.ToLower().EndsWith(".jpg") || saveDialog.FileName.ToLower().EndsWith(".jpeg"))
                        {
                            format = System.Windows.Forms.DataVisualization.Charting.ChartImageFormat.Jpeg;
                        }
                        else if (saveDialog.FileName.ToLower().EndsWith(".bmp"))
                        {
                            format = System.Windows.Forms.DataVisualization.Charting.ChartImageFormat.Bmp;
                        }

                        // 保存图表为图像
                        chartBrittleness.SaveImage(saveDialog.FileName, format);

                        // 恢复缩放状态
                        if (isZoomedY)
                        {
                            chartArea.AxisY.ScaleView.Zoom(currentMinY, currentMaxY);
                        }
                        if (isZoomedX)
                        {
                            chartArea.AxisX.ScaleView.Zoom(currentMinX, currentMaxX);
                        }

                        MessageBox.Show($"脆性指数曲线图已保存到: {saveDialog.FileName}", "保存成功",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        LoggingService.Instance.Info($"脆性指数曲线图已保存到: {saveDialog.FileName}");
                    }
                    catch (Exception saveEx)
                    {
                        MessageBox.Show($"保存图像时出错: {saveEx.Message}", "保存失败",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        LoggingService.Instance.Error($"保存图像失败: {saveEx.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存曲线失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"保存脆性指数曲线失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 紧急退出按钮 - 参考原系统，保存对比图数据
        /// </summary>
        private void btnEmergencyExit_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("开始保存对比图数据");

                // 详细检查数据状态
                LoggingService.Instance.Info($"chartBrittleness状态: {(chartBrittleness == null ? "null" : "已初始化")}");
                LoggingService.Instance.Info($"chartBrittleness.Series.Count: {(chartBrittleness?.Series.Count ?? 0)}");
                LoggingService.Instance.Info($"chartDataPoints.Count: {chartDataPoints.Count}");
                LoggingService.Instance.Info($"mechanicsData行数: {(mechanicsData?.Rows.Count ?? 0)}");

                // 检查是否有数据可以保存
                bool hasChartData = chartBrittleness != null && chartBrittleness.Series.Count > 0;
                bool hasDataPoints = chartDataPoints.Count > 0;
                bool hasMechanicsData = mechanicsData != null && mechanicsData.Rows.Count > 0;

                if (!hasChartData && !hasDataPoints && !hasMechanicsData)
                {
                    LoggingService.Instance.Warning("没有任何数据可以保存");
                    MessageBox.Show("没有图表数据可以保存为对比图！请先导入数据并生成曲线。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 如果有mechanicsData但没有chartDataPoints，尝试从mechanicsData生成数据点
                if (hasMechanicsData && !hasDataPoints)
                {
                    LoggingService.Instance.Info("从mechanicsData生成chartDataPoints");
                    GenerateChartDataPointsFromMechanicsData();
                }

                // 再次检查
                if (chartDataPoints.Count == 0)
                {
                    LoggingService.Instance.Warning("生成数据点后仍然没有数据");
                    MessageBox.Show("没有有效的数据点可以保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 保存当前图表数据到全局存储
                SaveChartDataForComparison();

                // 同时保存到历史数据中
                SaveToHistoryData();

                MessageBox.Show($"图表数据已保存，共 {chartDataPoints.Count} 个数据点！可以在其他系统中查看对比图，也可以查看历史数据对比。", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"保存对比图数据失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                MessageBox.Show($"保存对比图数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 从mechanicsData生成chartDataPoints
        /// </summary>
        private void GenerateChartDataPointsFromMechanicsData()
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                    return;

                chartDataPoints.Clear();

                // 智能识别列名
                string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
                string brittlenessColumnName = "脆性指数/%";

                if (string.IsNullOrEmpty(depthColumnName))
                {
                    depthColumnName = "深度/m";
                }

                if (!mechanicsData.Columns.Contains(brittlenessColumnName))
                {
                    LoggingService.Instance.Warning("未找到脆性指数列");
                    return;
                }

                int rowIndex = 0;
                foreach (DataRow row in mechanicsData.Rows)
                {
                    try
                    {
                        if (row[depthColumnName] != DBNull.Value && row[brittlenessColumnName] != DBNull.Value)
                        {
                            double depth = Convert.ToDouble(row[depthColumnName]);
                            double brittleness = Convert.ToDouble(row[brittlenessColumnName]);

                            chartDataPoints.Add(new InternalDataPoint
                            {
                                Depth = depth,
                                BrittlenessIndex = brittleness,
                                RowIndex = rowIndex
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"处理数据行 {rowIndex} 时出错: {ex.Message}");
                    }
                    rowIndex++;
                }

                LoggingService.Instance.Info($"从mechanicsData生成了 {chartDataPoints.Count} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"生成数据点失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存图表数据用于对比 - 参考原系统实现，增加数据相似度分析功能
        /// </summary>
        private void SaveChartDataForComparison()
        {
            try
            {
                // 从chartBrittleness图表控件中直接提取所有数据点
                var allDataPoints = new List<object>();

                if (chartBrittleness != null && chartBrittleness.Series.Count > 0)
                {
                    // 遍历所有系列（通常只有一个主系列，但可能有高亮系列等）
                    foreach (var series in chartBrittleness.Series)
                    {
                        // 跳过高亮点系列
                        if (series.Name == "高亮点" || series.Name == "HighlightPoint")
                            continue;

                        foreach (var point in series.Points)
                        {
                            double brittleIndex = point.XValue; // X轴是脆性指数
                            double depth = point.YValues[0];    // Y轴是深度

                            // 确保数据有效
                            if (brittleIndex >= 0 && brittleIndex <= 100 && depth > 0)
                            {
                                allDataPoints.Add(new
                                {
                                    TopDepth = depth,
                                    BottomDepth = depth,
                                    BrittleIndex = brittleIndex
                                });
                            }
                        }
                    }
                }

                // 如果图表中没有数据，尝试从chartDataPoints获取
                if (allDataPoints.Count == 0 && chartDataPoints.Count > 0)
                {
                    allDataPoints = chartDataPoints.Select(p => new
                    {
                        TopDepth = p.Depth,
                        BottomDepth = p.Depth,
                        BrittleIndex = p.BrittlenessIndex
                    }).Cast<object>().ToList();
                }

                // 生成数据统计信息
                var statistics = GenerateDataStatisticsFromPoints(allDataPoints);

                // 创建对比数据结构 - 包含从图表中提取的所有数据点
                var comparisonData = new
                {
                    SystemName = "增强版静态岩石力学参数法",
                    DataPoints = allDataPoints,
                    GeneratedTime = DateTime.Now,
                    TotalPoints = allDataPoints.Count,
                    Statistics = statistics,
                    DataSource = currentExcelFile ?? "手动输入",
                    Version = "2.0",
                    // 添加图表信息
                    ChartSeriesCount = chartBrittleness?.Series?.Count ?? 0,
                    OriginalDataCount = mechanicsData?.Rows.Count ?? 0
                };

                // 保存到临时文件（用于系统间对比）
                string tempPath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");
                string jsonData = Newtonsoft.Json.JsonConvert.SerializeObject(comparisonData, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(tempPath, jsonData);

                // 同时保存到历史数据（用于历史对比功能）
                SaveToHistoryData();

                LoggingService.Instance.Info($"对比图数据已保存到: {tempPath}，从图表提取 {allDataPoints.Count} 个数据点，图表系列数: {chartBrittleness?.Series?.Count ?? 0}");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"保存对比图数据失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成数据统计信息，用于相似度分析
        /// </summary>
        private object GenerateDataStatistics()
        {
            if (chartDataPoints.Count == 0)
                return new { };

            var brittlenessValues = chartDataPoints.Select(p => p.BrittlenessIndex).ToList();
            var depthValues = chartDataPoints.Select(p => p.Depth).ToList();

            return new
            {
                BrittlenessIndex = new
                {
                    Min = brittlenessValues.Min(),
                    Max = brittlenessValues.Max(),
                    Average = brittlenessValues.Average(),
                    StandardDeviation = CalculateStandardDeviation(brittlenessValues),
                    Median = CalculateMedian(brittlenessValues)
                },
                Depth = new
                {
                    Min = depthValues.Min(),
                    Max = depthValues.Max(),
                    Range = depthValues.Max() - depthValues.Min()
                },
                DataQuality = new
                {
                    ValidPoints = chartDataPoints.Count,
                    DepthSpacing = depthValues.Count > 1 ? (depthValues.Max() - depthValues.Min()) / (depthValues.Count - 1) : 0,
                    BrittlenessDistribution = CategorizeBrittleness(brittlenessValues)
                }
            };
        }

        /// <summary>
        /// 计算标准差
        /// </summary>
        private double CalculateStandardDeviation(List<double> values)
        {
            if (values.Count <= 1) return 0;

            double average = values.Average();
            double sumOfSquares = values.Sum(x => Math.Pow(x - average, 2));
            return Math.Sqrt(sumOfSquares / (values.Count - 1));
        }

        /// <summary>
        /// 计算中位数
        /// </summary>
        private double CalculateMedian(List<double> values)
        {
            var sorted = values.OrderBy(x => x).ToList();
            int count = sorted.Count;

            if (count % 2 == 0)
                return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
            else
                return sorted[count / 2];
        }

        /// <summary>
        /// 脆性指数分类统计
        /// </summary>
        private object CategorizeBrittleness(List<double> values)
        {
            int low = values.Count(v => v < 30);      // 低脆性
            int medium = values.Count(v => v >= 30 && v < 60); // 中脆性
            int high = values.Count(v => v >= 60);    // 高脆性

            return new
            {
                Low = low,
                Medium = medium,
                High = high,
                LowPercentage = values.Count > 0 ? (low * 100.0 / values.Count) : 0,
                MediumPercentage = values.Count > 0 ? (medium * 100.0 / values.Count) : 0,
                HighPercentage = values.Count > 0 ? (high * 100.0 / values.Count) : 0
            };
        }

        /// <summary>
        /// 从数据点列表生成统计信息
        /// </summary>
        private object GenerateDataStatisticsFromPoints(List<object> dataPoints)
        {
            if (dataPoints.Count == 0)
                return new { };

            try
            {
                var brittlenessValues = new List<double>();
                var depthValues = new List<double>();

                foreach (var point in dataPoints)
                {
                    var pointData = point as dynamic;
                    if (pointData?.BrittleIndex != null && pointData?.TopDepth != null)
                    {
                        brittlenessValues.Add(Convert.ToDouble(pointData.BrittleIndex));
                        depthValues.Add(Convert.ToDouble(pointData.TopDepth));
                    }
                }

                if (brittlenessValues.Count == 0)
                    return new { };

                return new
                {
                    BrittlenessIndex = new
                    {
                        Min = brittlenessValues.Min(),
                        Max = brittlenessValues.Max(),
                        Average = brittlenessValues.Average(),
                        StandardDeviation = CalculateStandardDeviation(brittlenessValues),
                        Median = CalculateMedian(brittlenessValues)
                    },
                    Depth = new
                    {
                        Min = depthValues.Min(),
                        Max = depthValues.Max(),
                        Range = depthValues.Max() - depthValues.Min()
                    },
                    DataQuality = new
                    {
                        ValidPoints = dataPoints.Count,
                        DepthSpacing = depthValues.Count > 1 ? (depthValues.Max() - depthValues.Min()) / (depthValues.Count - 1) : 0,
                        BrittlenessDistribution = CategorizeBrittleness(brittlenessValues)
                    }
                };
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"生成统计信息时出错: {ex.Message}");
                return new { Error = ex.Message };
            }
        }

        private void btnViewComparison_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("开始查看对比图");

                // 使用简化版本的对比图功能
                ShowSimpleComparisonChart();
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"查看对比图失败: {ex.Message}");
                MessageBox.Show($"查看对比图失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowSimpleComparisonChart()
        {
            try
            {
                // 检查临时文件是否存在
                string mineralDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_MineralogicalData.json");
                string staticDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");

                bool hasMineralData = File.Exists(mineralDataPath);
                bool hasStaticData = File.Exists(staticDataPath);

                if (!hasMineralData && !hasStaticData)
                {
                    // 如果没有找到数据，先保存当前数据
                    if (chartDataPoints.Count > 0 || (mechanicsData != null && mechanicsData.Rows.Count > 0))
                    {
                        // 如果有mechanicsData但没有chartDataPoints，先生成chartDataPoints
                        if (chartDataPoints.Count == 0 && mechanicsData != null && mechanicsData.Rows.Count > 0)
                        {
                            GenerateChartDataPointsFromMechanicsData();
                        }

                        if (chartDataPoints.Count > 0)
                        {
                            SaveChartDataForComparison();
                            hasStaticData = true;
                        }
                        else
                        {
                            MessageBox.Show("没有找到对比数据！请先计算一些数据点，或在其他系统中保存图表数据。",
                                "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }
                    }
                    else
                    {
                        MessageBox.Show("没有找到对比数据！请先计算一些数据点，或在其他系统中保存图表数据。",
                            "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                }

                // 创建简化的对比图窗体
                var comparisonForm = new SimpleComparisonChartForm();
                comparisonForm.LoadComparisonData(mineralDataPath, staticDataPath, hasMineralData, hasStaticData);
                comparisonForm.ShowDialog();

                LoggingService.Instance.Info("对比图显示完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"显示对比图失败: {ex.Message}");
                MessageBox.Show($"显示对比图失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnEnhancedAnalysis_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("启动增强分析算法功能");

                // 检查是否有计算后的数据（包含脆性指数）
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("请先导入数据或进行计算，然后再进行增强分析。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查是否有脆性指数数据
                if (!mechanicsData.Columns.Contains("脆性指数/%"))
                {
                    MessageBox.Show("请先计算脆性指数，然后再进行增强分析。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查脆性指数数据是否有效
                bool hasValidData = false;
                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (row["脆性指数/%"] != DBNull.Value)
                    {
                        hasValidData = true;
                        break;
                    }
                }

                if (!hasValidData)
                {
                    MessageBox.Show("没有有效的脆性指数数据，请先进行计算。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 执行增强分析
                var analysisResult = await PerformEnhancedAnalysis();

                // 显示可视化分析结果
                var resultForm = new EnhancedAnalysisResultForm(mechanicsData, analysisResult);
                resultForm.ShowDialog();

                LoggingService.Instance.Info("增强分析算法执行完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动增强分析功能失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"启动增强分析功能失败: {ex.Message}");
            }
        }

        private async Task<string> PerformEnhancedAnalysis()
        {
            return await Task.Run(() =>
            {
                var result = new System.Text.StringBuilder();
                result.AppendLine("=== 增强版静态岩石力学参数分析报告 ===\n");

                // 从mechanicsData中提取有效的脆性指数数据
                var validBrittlenessData = new List<double>();
                var validYoungModulusData = new List<double>();
                var validPoissonRatioData = new List<double>();

                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (row["脆性指数/%"] != DBNull.Value)
                    {
                        validBrittlenessData.Add(Convert.ToDouble(row["脆性指数/%"]));
                    }
                    if (row["动态杨氏模量/GPa"] != DBNull.Value)
                    {
                        validYoungModulusData.Add(Convert.ToDouble(row["动态杨氏模量/GPa"]));
                    }
                    if (row["动态泊松比"] != DBNull.Value)
                    {
                        validPoissonRatioData.Add(Convert.ToDouble(row["动态泊松比"]));
                    }
                }

                // 基础统计分析
                result.AppendLine("【基础统计分析】");
                result.AppendLine($"有效数据点总数: {validBrittlenessData.Count}");

                if (validBrittlenessData.Count > 0)
                {
                    result.AppendLine($"脆性指数范围: {validBrittlenessData.Min():F2} - {validBrittlenessData.Max():F2}");
                    result.AppendLine($"脆性指数平均值: {validBrittlenessData.Average():F2}");
                }

                if (validYoungModulusData.Count > 0)
                {
                    result.AppendLine($"杨氏模量范围: {validYoungModulusData.Min():F2} - {validYoungModulusData.Max():F2} GPa");
                }

                if (validPoissonRatioData.Count > 0)
                {
                    result.AppendLine($"泊松比范围: {validPoissonRatioData.Min():F3} - {validPoissonRatioData.Max():F3}");
                }
                result.AppendLine();

                // 岩石分类分析
                result.AppendLine("【岩石脆性分类分析】");
                if (validBrittlenessData.Count > 0)
                {
                    var brittleCount = validBrittlenessData.Count(bi => bi > 60);
                    var moderateCount = validBrittlenessData.Count(bi => bi >= 40 && bi <= 60);
                    var ductileCount = validBrittlenessData.Count(bi => bi < 40);

                    result.AppendLine($"高脆性岩石 (BI > 60): {brittleCount} 个 ({(double)brittleCount / validBrittlenessData.Count * 100:F1}%)");
                    result.AppendLine($"中等脆性岩石 (40 ≤ BI ≤ 60): {moderateCount} 个 ({(double)moderateCount / validBrittlenessData.Count * 100:F1}%)");
                    result.AppendLine($"低脆性岩石 (BI < 40): {ductileCount} 个 ({(double)ductileCount / validBrittlenessData.Count * 100:F1}%)");
                }
                else
                {
                    result.AppendLine("无有效的脆性指数数据进行分类分析");
                }
                result.AppendLine();

                // 相关性分析
                result.AppendLine("【参数相关性分析】");
                if (mechanicsData.Rows.Count > 1)
                {
                    // 从mechanicsData中提取相关数据进行相关性分析
                    var densityData = new List<double>();
                    var vpData = new List<double>();
                    var vsData = new List<double>();
                    var brittlenessData = new List<double>();

                    foreach (DataRow row in mechanicsData.Rows)
                    {
                        if (row["密度/(g/cm³)"] != DBNull.Value && row["脆性指数/%"] != DBNull.Value)
                        {
                            densityData.Add(Convert.ToDouble(row["密度/(g/cm³)"]));
                            brittlenessData.Add(Convert.ToDouble(row["脆性指数/%"]));
                        }
                    }

                    if (densityData.Count > 1)
                    {
                        var densityBICorr = CalculateCorrelation(densityData.ToArray(), brittlenessData.ToArray());
                        result.AppendLine($"密度与脆性指数相关系数: {densityBICorr:F3}");
                    }
                    else
                    {
                        result.AppendLine("数据量不足，无法进行相关性分析");
                    }
                }
                else
                {
                    result.AppendLine("数据量不足，无法进行相关性分析");
                }
                result.AppendLine();

                // 建议和结论
                result.AppendLine("【分析建议】");
                if (validBrittlenessData.Count > 0)
                {
                    var brittleCount = validBrittlenessData.Count(bi => bi > 60);
                    var ductileCount = validBrittlenessData.Count(bi => bi < 40);

                    if (brittleCount > validBrittlenessData.Count * 0.6)
                    {
                        result.AppendLine("• 该区域岩石整体表现为高脆性特征，适合压裂改造");
                    }
                    else if (ductileCount > validBrittlenessData.Count * 0.6)
                    {
                        result.AppendLine("• 该区域岩石整体表现为低脆性特征，压裂改造效果可能有限");
                    }
                    else
                    {
                        result.AppendLine("• 该区域岩石脆性特征分布较为均匀，建议分层分段压裂");
                    }
                }
                else
                {
                    result.AppendLine("• 无有效数据，无法提供分析建议");
                }

                result.AppendLine($"• 分析完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                return result.ToString();
            });
        }

        private double CalculateCorrelation(double[] x, double[] y)
        {
            if (x.Length != y.Length || x.Length == 0)
                return 0;

            double meanX = x.Average();
            double meanY = y.Average();

            double numerator = x.Zip(y, (xi, yi) => (xi - meanX) * (yi - meanY)).Sum();
            double denominatorX = Math.Sqrt(x.Sum(xi => Math.Pow(xi - meanX, 2)));
            double denominatorY = Math.Sqrt(y.Sum(yi => Math.Pow(yi - meanY, 2)));

            if (denominatorX == 0 || denominatorY == 0)
                return 0;

            return numerator / (denominatorX * denominatorY);
        }



        #region 自适应缩放功能

        /// <summary>
        /// 初始化缩放功能，记录原始控件位置和字体
        /// </summary>
        private void InitializeScaling()
        {
            try
            {
                // 记录所有控件的原始位置和字体
                RecordOriginalBounds(this);

                // 绑定窗体大小改变事件
                this.Resize += StaticRockMechanicsForm_Resize;

                LoggingService.Instance.Info("自适应缩放功能初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"初始化自适应缩放功能失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归记录控件的原始位置和字体
        /// </summary>
        private void RecordOriginalBounds(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                originalControlBounds[control] = control.Bounds;
                originalControlFonts[control] = control.Font;

                if (control.HasChildren)
                {
                    RecordOriginalBounds(control);
                }
            }
        }

        /// <summary>
        /// 窗体大小改变事件处理
        /// </summary>
        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            if (originalControlBounds.Count > 0)
            {
                ResizeControls(this);
            }
        }

        /// <summary>
        /// 递归调整控件大小和位置
        /// </summary>
        private void ResizeControls(Control parent)
        {
            float scaleX = (float)this.ClientSize.Width / originalFormSize.Width;
            float scaleY = (float)this.ClientSize.Height / originalFormSize.Height;

            foreach (Control control in parent.Controls)
            {
                if (originalControlBounds.ContainsKey(control))
                {
                    Rectangle originalBounds = originalControlBounds[control];
                    Font originalFont = originalControlFonts[control];

                    // 调整控件位置和大小
                    control.Left = (int)(originalBounds.Left * scaleX);
                    control.Top = (int)(originalBounds.Top * scaleY);
                    control.Width = (int)(originalBounds.Width * scaleX);
                    control.Height = (int)(originalBounds.Height * scaleY);

                    // 调整字体大小
                    float newFontSize = originalFont.Size * Math.Min(scaleX, scaleY);
                    if (newFontSize > 6 && newFontSize < 72) // 限制字体大小范围
                    {
                        try
                        {
                            control.Font = new Font(originalFont.FontFamily, newFontSize, originalFont.Style);
                        }
                        catch
                        {
                            // 如果字体创建失败，保持原字体
                        }
                    }
                }

                if (control.HasChildren)
                {
                    ResizeControls(control);
                }
            }
        }

        /// <summary>
        /// 批量计算数据 - 完全参考原系统BritSystem
        /// </summary>
        private void CalculateBatchData()
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以计算！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                int successCount = 0;
                int errorCount = 0;
                var results = new List<RockMechanicsResult>();
                var allEs = new List<double>();
                var allMuS = new List<double>();

                // 第一遍：计算所有数据的Es和MuS，用于确定动态范围
                for (int i = 0; i < mechanicsData.Rows.Count; i++)
                {
                    try
                    {
                        DataRow row = mechanicsData.Rows[i];

                        double density = Convert.ToDouble(row["密度/(g/cm³)"]);
                        double vp = Convert.ToDouble(row["纵波速度/(m/s)"]);
                        double vs = Convert.ToDouble(row["横波速度/(m/s)"]);

                        // 进行单位转换
                        double convertedDensity = ConvertDensityToStandard(density);
                        double convertedVp = ConvertVelocityToStandard(vp, true);
                        double convertedVs = ConvertVelocityToStandard(vs, false);

                        // 数据验证
                        if (convertedVp <= convertedVs)
                        {
                            LoggingService.Instance.Warning($"第{i + 1}行数据不合理：纵波速度({convertedVp})应大于横波速度({convertedVs})");
                            results.Add(new RockMechanicsResult()); // 添加空结果
                            errorCount++;
                            continue;
                        }

                        // 计算岩石力学参数
                        var result = CalculateStaticRockMechanics(convertedDensity, convertedVp, convertedVs);
                        results.Add(result);

                        if (result.Es > 0 && result.MuS > 0)
                        {
                            allEs.Add(result.Es);
                            allMuS.Add(result.MuS);
                            successCount++;
                        }
                        else
                        {
                            errorCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"第{i + 1}行数据计算失败: {ex.Message}");
                        results.Add(new RockMechanicsResult()); // 添加空结果
                        errorCount++;
                    }
                }

                // 使用数据集动态范围计算脆性指数 - 与公式计算.cs保持一致
                double EsMin = allEs.Count > 0 ? allEs.Min() : 0;
                double EsMax = allEs.Count > 0 ? allEs.Max() : 100;
                double MuSMin = allMuS.Count > 0 ? allMuS.Min() : 0;
                double MuSMax = allMuS.Count > 0 ? allMuS.Max() : 1;

                // 第二遍：使用数据集动态范围计算脆性指数并保存结果
                for (int i = 0; i < mechanicsData.Rows.Count; i++)
                {
                    var result = results[i];
                    if (result.Es > 0) // 有效结果
                    {
                        // 使用数据集动态范围计算脆性指数
                        result.BrittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, EsMin, EsMax, MuSMin, MuSMax);

                        // 保存计算结果
                        mechanicsData.Rows[i]["动态杨氏模量/GPa"] = result.Ed;
                        mechanicsData.Rows[i]["动态泊松比"] = result.MuD;
                        mechanicsData.Rows[i]["静态杨氏模量/GPa"] = result.Es;
                        mechanicsData.Rows[i]["静态泊松比"] = result.MuS;
                        mechanicsData.Rows[i]["脆性指数/%"] = result.BrittlenessIndex;
                    }
                }

                // 更新数据表格显示
                dgvMechanicsData.DataSource = null;
                dgvMechanicsData.DataSource = mechanicsData;

                // 更新图表
                UpdateChart();

                MessageBox.Show($"批量计算完成！\n共处理 {mechanicsData.Rows.Count} 条数据\n所有数据均已完成计算和分析",
                    "计算完成", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoggingService.Instance.Info($"批量计算完成: 成功 {successCount} 条，失败 {errorCount} 条");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"批量计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"批量计算失败: {ex.Message}");
            }
        }

        #endregion

        #region 岩石力学计算方法

        /// <summary>
        /// 根据原系统公式计算静态岩石力学参数
        /// </summary>
        /// <param name="density">岩石密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        private RockMechanicsResult CalculateStaticRockMechanics(double density, double vp, double vs)
        {
            var result = new RockMechanicsResult();

            // 数据验证：检查Vp > √2 * Vs
            if (vp <= Math.Sqrt(2) * vs)
            {
                // 数据异常，但仍继续计算，只是标记异常
                System.Diagnostics.Debug.WriteLine($"警告: Vp({vp}) <= √2·Vs({vs * Math.Sqrt(2):F2})，数据可能异常");
            }

            // 计算动态杨氏模量 Ed (GPa)
            // 使用文献公式：Ed = 10^-3 × ρ × Vs^2 × (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)
            // 输入：ρ (g/cm³), Vp (m/s), Vs (m/s)
            // 输出：Ed (GPa)
            double vp2 = vp * vp;
            double vs2 = vs * vs;
            double denominator = vp2 - vs2;

            // 防止除以零
            if (Math.Abs(denominator) < 1e-10)
            {
                result.Ed = 0;
            }
            else
            {
                // 完全按照公式计算.cs的实现（相信这是正确的）
                result.Ed = 1e-3 * density * vs2 * (3 * vp2 - 4 * vs2) / denominator;
            }

            // 计算动态泊松比 μd
            // μd = (Vp^2 - 2Vs^2) / (2(Vp^2 - Vs^2))
            result.MuD = (vp2 - 2 * vs2) / (2 * denominator);

            // 计算静态杨氏模量 Es (GPa)
            // Es = Ed × 0.5823 + 7.566
            result.Es = result.Ed * 0.5823 + 7.566;

            // 计算静态泊松比 μs
            // μs = μd × 0.6648 + 0.0514
            result.MuS = result.MuD * 0.6648 + 0.0514;

            // 脆性指数计算将在批量计算时使用动态范围
            // 这里先设置为0，在批量计算时会重新计算
            result.BrittlenessIndex = 0;

            return result;
        }

        /// <summary>
        /// 使用动态范围计算脆性指数
        /// </summary>
        /// <param name="Es">静态杨氏模量</param>
        /// <param name="MuS">静态泊松比</param>
        /// <param name="EsMin">数据集中Es的最小值</param>
        /// <param name="EsMax">数据集中Es的最大值</param>
        /// <param name="MuSMin">数据集中MuS的最小值</param>
        /// <param name="MuSMax">数据集中MuS的最大值</param>
        /// <returns>脆性指数</returns>
        private double CalculateBrittlenessIndex(double Es, double MuS, double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量脆性指数
            // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
            double EBRIT = 0;
            if (EsMax > EsMin)
            {
                EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内
            }

            // 归一化泊松比脆性指数
            // PBRIT = (μmax - μs) / (μmax - μmin) × 100%
            double PBRIT = 0;
            if (MuSMax > MuSMin)
            {
                PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
                PBRIT = Math.Max(0, Math.Min(100, PBRIT)); // 限制在0-100%范围内
            }

            // 综合脆性指数
            // BRIT = (EBRIT + PBRIT) / 2
            double BRIT = (EBRIT + PBRIT) / 2;

            return BRIT;
        }

        #endregion

        #region 单位转换方法

        /// <summary>
        /// 将密度转换为标准单位 (g/cm³)
        /// </summary>
        private double ConvertDensityToStandard(double inputValue)
        {
            // 根据用户说明，系统中的密度实际单位是kg/m³，需要转换为g/cm³
            // 转换公式：ρ(g/cm³) = ρ(kg/m³) ÷ 1000
            return inputValue / 1000.0;
        }

        /// <summary>
        /// 将速度转换为标准单位 (m/s)
        /// 修正：根据用户分析，系统中的数据实际是时差值，需要正确转换
        /// </summary>
        /// <param name="inputValue">输入值</param>
        /// <param name="isVp">是否为纵波速度</param>
        /// <returns>转换后的速度值</returns>
        private double ConvertVelocityToStandard(double inputValue, bool isVp)
        {
            if (isVp) // 纵波速度
            {
                if (rbVelocityDt.Checked)
                {
                    // DT (μs/m) 转换为 Vp (m/s)
                    // Vp = 1,000,000 / DT
                    return 1000000.0 / inputValue;
                }
                else
                {
                    // 修正：系统中的数据实际是时差值，需要转换为速度
                    // 纵波速度：Vp = 1,000,000 / DT (μs/m)
                    return 1000000.0 / inputValue;
                }
            }
            else // 横波速度
            {
                if (rbVelocityDts.Checked)
                {
                    // DTS (μs/m) 转换为 Vs (m/s)
                    // Vs = 1,000,000 / DTS
                    return 1000000.0 / inputValue;
                }
                else
                {
                    // 修正：系统中的数据实际是时差值，需要转换为速度
                    // 横波速度：Vs = 1,000,000 / DTS (μs/m)
                    return 1000000.0 / inputValue;
                }
            }
        }

        #endregion

        #region Excel读写方法 - 完全参考原系统BritSystem

        /// <summary>
        /// 读取Excel文件 - 完全参考原系统BritSystem
        /// </summary>
        private DataSet ReadExcelSheets(string filePath)
        {
            try
            {
                DataSet dataSet = new DataSet();

                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = null;

                    if (Path.GetExtension(filePath).ToLower() == ".xls")
                    {
                        workbook = new HSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(stream);
                    }

                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        ISheet sheet = workbook.GetSheetAt(i);
                        DataTable dataTable = new DataTable(sheet.SheetName);

                        // 读取表头
                        IRow headerRow = sheet.GetRow(0);
                        if (headerRow != null)
                        {
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = headerRow.GetCell(j);
                                string columnName = cell?.ToString() ?? $"Column{j + 1}";
                                dataTable.Columns.Add(columnName, typeof(object));
                            }
                        }

                        // 读取数据行
                        for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow row = sheet.GetRow(rowIndex);
                            if (row != null)
                            {
                                DataRow dataRow = dataTable.NewRow();
                                for (int cellIndex = 0; cellIndex < dataTable.Columns.Count; cellIndex++)
                                {
                                    ICell cell = row.GetCell(cellIndex);
                                    if (cell != null)
                                    {
                                        switch (cell.CellType)
                                        {
                                            case CellType.Numeric:
                                                dataRow[cellIndex] = cell.NumericCellValue;
                                                break;
                                            case CellType.String:
                                                dataRow[cellIndex] = cell.StringCellValue;
                                                break;
                                            case CellType.Boolean:
                                                dataRow[cellIndex] = cell.BooleanCellValue;
                                                break;
                                            case CellType.Formula:
                                                try
                                                {
                                                    dataRow[cellIndex] = cell.NumericCellValue;
                                                }
                                                catch
                                                {
                                                    dataRow[cellIndex] = cell.StringCellValue;
                                                }
                                                break;
                                            default:
                                                dataRow[cellIndex] = cell.ToString();
                                                break;
                                        }
                                    }
                                }
                                dataTable.Rows.Add(dataRow);
                            }
                        }

                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 智能识别列名 - 完全参考原系统BritSystem
        /// </summary>
        private string? FindColumnByPattern(DataTable dataTable, string patternKey)
        {
            if (!columnPatterns.ContainsKey(patternKey))
                return null;

            var patterns = columnPatterns[patternKey];

            foreach (DataColumn column in dataTable.Columns)
            {
                string columnName = column.ColumnName.Trim().ToLower();
                foreach (string pattern in patterns)
                {
                    if (columnName.Contains(pattern.ToLower()))
                    {
                        return column.ColumnName;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 将导入的数据映射到标准格式 - 完全参考原系统BritSystem
        /// </summary>
        private DataTable? MapImportedDataToStandardFormat(DataTable importedData)
        {
            try
            {
                // 智能识别列名
                string? depthColumn = FindColumnByPattern(importedData, "深度");
                string? densityColumn = FindColumnByPattern(importedData, "密度");
                string? vpColumn = FindColumnByPattern(importedData, "纵波速度");
                string? vsColumn = FindColumnByPattern(importedData, "横波速度");

                // 检查必需的列是否存在
                if (string.IsNullOrEmpty(densityColumn) || string.IsNullOrEmpty(vpColumn) || string.IsNullOrEmpty(vsColumn))
                {
                    LoggingService.Instance.Warning($"缺少必需的列: 密度={densityColumn}, 纵波速度={vpColumn}, 横波速度={vsColumn}");
                    return null;
                }

                // 创建标准格式的数据表
                DataTable standardData = new DataTable();
                standardData.Columns.Add("深度/m", typeof(double)); // 修改为只使用深度列
                standardData.Columns.Add("密度/(g/cm³)", typeof(double));
                standardData.Columns.Add("纵波速度/(m/s)", typeof(double));
                standardData.Columns.Add("横波速度/(m/s)", typeof(double));
                standardData.Columns.Add("动态杨氏模量/GPa", typeof(double));
                standardData.Columns.Add("动态泊松比", typeof(double));
                standardData.Columns.Add("静态杨氏模量/GPa", typeof(double));
                standardData.Columns.Add("静态泊松比", typeof(double));
                standardData.Columns.Add("脆性指数/%", typeof(double));

                // 映射数据
                foreach (DataRow sourceRow in importedData.Rows)
                {
                    try
                    {
                        DataRow newRow = standardData.NewRow();

                        // 深度数据
                        if (!string.IsNullOrEmpty(depthColumn) && sourceRow[depthColumn] != DBNull.Value)
                        {
                            double depth = Convert.ToDouble(sourceRow[depthColumn]);
                            newRow["深度/m"] = depth;
                        }
                        else
                        {
                            newRow["深度/m"] = 0.0;
                        }

                        // 密度数据
                        if (sourceRow[densityColumn] != DBNull.Value)
                        {
                            newRow["密度/(g/cm³)"] = Convert.ToDouble(sourceRow[densityColumn]);
                        }

                        // 纵波速度数据
                        if (sourceRow[vpColumn] != DBNull.Value)
                        {
                            newRow["纵波速度/(m/s)"] = Convert.ToDouble(sourceRow[vpColumn]);
                        }

                        // 横波速度数据
                        if (sourceRow[vsColumn] != DBNull.Value)
                        {
                            newRow["横波速度/(m/s)"] = Convert.ToDouble(sourceRow[vsColumn]);
                        }

                        // 初始化计算结果列为0
                        newRow["动态杨氏模量/GPa"] = 0.0;
                        newRow["动态泊松比"] = 0.0;
                        newRow["静态杨氏模量/GPa"] = 0.0;
                        newRow["静态泊松比"] = 0.0;
                        newRow["脆性指数/%"] = 0.0;

                        standardData.Rows.Add(newRow);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"映射数据行时出错: {ex.Message}");
                    }
                }

                LoggingService.Instance.Info($"数据映射完成，共 {standardData.Rows.Count} 行有效数据");
                return standardData;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"数据映射失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 导出Excel文件 - 完全参考原系统BritSystem
        /// </summary>
        private void ExportExcelFile(string filePath)
        {
            try
            {
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("静态岩石力学参数数据");

                // 创建表头
                IRow headerRow = sheet.CreateRow(0);
                for (int i = 0; i < mechanicsData.Columns.Count; i++)
                {
                    headerRow.CreateCell(i).SetCellValue(mechanicsData.Columns[i].ColumnName);
                }

                // 写入数据
                for (int i = 0; i < mechanicsData.Rows.Count; i++)
                {
                    IRow dataRow = sheet.CreateRow(i + 1);
                    for (int j = 0; j < mechanicsData.Columns.Count; j++)
                    {
                        var cellValue = mechanicsData.Rows[i][j];
                        if (cellValue != null && cellValue != DBNull.Value)
                        {
                            if (double.TryParse(cellValue.ToString(), out double numValue))
                            {
                                dataRow.CreateCell(j).SetCellValue(numValue);
                            }
                            else
                            {
                                dataRow.CreateCell(j).SetCellValue(cellValue.ToString());
                            }
                        }
                    }
                }

                // 自动调整列宽
                for (int i = 0; i < mechanicsData.Columns.Count; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }

                LoggingService.Instance.Info($"数据导出成功: {filePath}，共 {mechanicsData.Rows.Count} 行数据");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导出Excel文件失败: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region 图表交互事件 - 参考原系统

        /// <summary>
        /// 图表鼠标滚轮事件 - 参考MineralogicalForm的高级缩放功能
        /// </summary>
        private void Chart_MouseWheel(object sender, MouseEventArgs e)
        {
            try
            {
                if (chartBrittleness == null || chartBrittleness.ChartAreas.Count == 0 || chartDataPoints.Count == 0)
                    return;

                var chartArea = chartBrittleness.ChartAreas[0];
                bool isZoomX = (ModifierKeys & Keys.Shift) == Keys.Shift; // Shift键缩放X轴
                double zoomDirection = e.Delta > 0 ? 1 : -1; // 向上滚动为正，向下滚动为负

                if (isZoomX)
                {
                    // X轴缩放 - 参考MineralogicalForm的实现
                    try
                    {
                        double newXZoom = currentXZoom * (zoomDirection > 0 ? ZOOM_FACTOR : 1 / ZOOM_FACTOR);
                        newXZoom = Math.Min(Math.Max(newXZoom, MIN_ZOOM), MAX_X_ZOOM);

                        if (Math.Abs(newXZoom - currentXZoom) < 0.01) return;

                        // 获取鼠标位置对应的X轴值
                        double xValue = chartArea.AxisX.PixelPositionToValue(e.X);

                        // 计算新的显示范围
                        double currentRange = chartArea.AxisX.ScaleView.ViewMaximum - chartArea.AxisX.ScaleView.ViewMinimum;
                        double newRange = currentRange / (newXZoom / currentXZoom);
                        double newMin = xValue - (newRange / 2);
                        double newMax = xValue + (newRange / 2);

                        // 确保不超出数据范围
                        if (newMin < 0)
                        {
                            newMax += (0 - newMin);
                            newMin = 0;
                        }
                        if (newMax > 100)
                        {
                            newMin -= (newMax - 100);
                            newMax = 100;
                        }

                        currentXZoom = newXZoom;
                        chartArea.AxisX.ScaleView.Zoom(newMin, newMax);

                        LoggingService.Instance.Info($"X轴缩放: 新范围=[{newMin:F2}, {newMax:F2}], 缩放比例={currentXZoom:F2}");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"X轴缩放出错: {ex.Message}");
                        chartArea.AxisX.ScaleView.ZoomReset();
                        currentXZoom = 1.0;
                    }
                }
                else
                {
                    // Y轴缩放 - 参考MineralogicalForm的实现
                    try
                    {
                        double newZoom = currentZoom * (zoomDirection > 0 ? ZOOM_FACTOR : 1 / ZOOM_FACTOR);
                        newZoom = Math.Min(Math.Max(newZoom, MIN_ZOOM), MAX_ZOOM);

                        if (Math.Abs(newZoom - currentZoom) < 0.01) return;

                        // 获取鼠标位置对应的Y轴值
                        double yValue = chartArea.AxisY.PixelPositionToValue(e.Y);

                        // 计算新的显示范围
                        double currentRange = chartArea.AxisY.ScaleView.ViewMaximum - chartArea.AxisY.ScaleView.ViewMinimum;
                        double newRange = currentRange / (newZoom / currentZoom);
                        double newMin = yValue - (newRange / 2);
                        double newMax = yValue + (newRange / 2);

                        // 确保不超出数据范围
                        double dataMin = chartDataPoints.Min(p => p.Depth);
                        double dataMax = chartDataPoints.Max(p => p.Depth);

                        if (newMin < dataMin)
                        {
                            newMax += (dataMin - newMin);
                            newMin = dataMin;
                        }
                        if (newMax > dataMax)
                        {
                            newMin -= (newMax - dataMax);
                            newMax = dataMax;
                        }

                        currentZoom = newZoom;
                        chartArea.AxisY.ScaleView.Zoom(newMin, newMax);

                        LoggingService.Instance.Info($"Y轴缩放: 新范围=[{newMin:F2}, {newMax:F2}], 缩放比例={currentZoom:F2}");
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"Y轴缩放出错: {ex.Message}");
                        chartArea.AxisY.ScaleView.ZoomReset();
                        currentZoom = 1.0;
                    }
                }

                // 强制重绘图表
                chartBrittleness.Invalidate();

                // 显示缩放提示
                string axisName = isZoomX ? "X轴" : "Y轴";
                double zoomValue = isZoomX ? currentXZoom : currentZoom;
                LoggingService.Instance.Info($"图表缩放完成: {axisName}缩放比例={zoomValue:F1}x");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"图表缩放事件出错: {ex.Message}");

                // 重置缩放
                try
                {
                    if (chartBrittleness != null && chartBrittleness.ChartAreas.Count > 0)
                    {
                        chartBrittleness.ChartAreas[0].AxisX.ScaleView.ZoomReset();
                        chartBrittleness.ChartAreas[0].AxisY.ScaleView.ZoomReset();
                        currentZoom = 1.0;
                        currentXZoom = 1.0;
                    }
                }
                catch
                {
                    // 忽略重置失败
                }
            }
        }

        /// <summary>
        /// 图表鼠标点击事件
        /// </summary>
        private void Chart_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                if (e.Button != MouseButtons.Left || chartDataPoints.Count == 0)
                    return;

                var chartArea = chartBrittleness.ChartAreas[0];

                // 将鼠标坐标转换为图表坐标
                double xValue = chartArea.AxisX.PixelPositionToValue(e.X);
                double yValue = chartArea.AxisY.PixelPositionToValue(e.Y);

                // 查找最近的数据点
                InternalDataPoint? nearestPoint = null;
                double minDistance = double.MaxValue;

                foreach (var point in chartDataPoints)
                {
                    double distance = Math.Sqrt(Math.Pow(point.BrittlenessIndex - xValue, 2) + Math.Pow(point.Depth - yValue, 2));
                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        nearestPoint = point;
                    }
                }

                // 如果找到了足够近的点，高亮显示
                if (nearestPoint.HasValue && minDistance < 10) // 距离阈值
                {
                    selectedRows.Clear();
                    selectedRows.Add(nearestPoint.Value.RowIndex);

                    // 高亮图表中的点
                    HighlightChartPoint(nearestPoint.Value);

                    // 选中数据表中对应的行
                    if (nearestPoint.Value.RowIndex < dgvMechanicsData.Rows.Count)
                    {
                        dgvMechanicsData.ClearSelection();
                        dgvMechanicsData.Rows[nearestPoint.Value.RowIndex].Selected = true;
                        dgvMechanicsData.FirstDisplayedScrollingRowIndex = nearestPoint.Value.RowIndex;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"图表点击事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// DataGridView单元格点击事件 - 参考MineralogicalForm的高效实现
        /// </summary>
        private void DataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
                {
                    LoggingService.Instance.Info($"点击数据表行: {e.RowIndex}");

                    selectedRows.Clear();
                    selectedRows.Add(e.RowIndex);

                    // 立即更新数据表选择状态
                    dgvMechanicsData.ClearSelection();
                    if (e.RowIndex < dgvMechanicsData.Rows.Count)
                    {
                        dgvMechanicsData.Rows[e.RowIndex].Selected = true;
                        dgvMechanicsData.CurrentCell = dgvMechanicsData.Rows[e.RowIndex].Cells[0];
                    }

                    // 查找对应的数据点
                    var matchingPoint = chartDataPoints.FirstOrDefault(p => p.RowIndex == e.RowIndex);
                    if (matchingPoint.RowIndex >= 0)
                    {
                        // 使用异步调用避免阻塞UI - 参考MineralogicalForm的实现
                        this.BeginInvoke(new Action(() =>
                        {
                            HighlightChartPoint(matchingPoint);
                        }));
                    }
                    else
                    {
                        LoggingService.Instance.Warning($"未找到行索引 {e.RowIndex} 对应的数据点");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"数据表点击事件出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 高亮显示图表中的点 - 参考MineralogicalForm的高效实现
        /// </summary>
        private void HighlightChartPoint(InternalDataPoint point)
        {
            try
            {
                LoggingService.Instance.Info($"开始高亮图表点: 深度={point.Depth:F2}m, 脆性指数={point.BrittlenessIndex:F2}%");

                // 清除现有的高亮系列
                var seriesToRemove = chartBrittleness.Series.Where(s => s.Name.StartsWith("Highlight") || s.Name.Contains("选中点")).ToList();
                foreach (Series series in seriesToRemove)
                {
                    chartBrittleness.Series.Remove(series);
                }

                // 创建新的高亮点系列 - 参考MineralogicalForm的样式
                string seriesName = $"HighlightPoints_{DateTime.Now.Ticks}";
                Series highlightSeries = new Series(seriesName)
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Gold,
                    MarkerSize = 12,
                    MarkerStyle = MarkerStyle.Circle,
                    MarkerBorderColor = Color.Red,
                    MarkerBorderWidth = 2,
                    LegendText = "选中点",
                    IsVisibleInLegend = false
                };

                // 添加高亮点
                highlightSeries.Points.AddXY(point.BrittlenessIndex, point.Depth);
                chartBrittleness.Series.Add(highlightSeries);

                // 使用异步更新UI，避免阻塞 - 参考MineralogicalForm的实现
                if (!this.IsDisposed && this.IsHandleCreated)
                {
                    this.BeginInvoke(new Action(() =>
                    {
                        if (chartBrittleness != null && !chartBrittleness.IsDisposed)
                        {
                            chartBrittleness.Update();
                            chartBrittleness.Invalidate();
                        }
                    }));
                }

                LoggingService.Instance.Info($"高亮图表点完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"高亮图表点时出错: {ex.Message}");
            }
        }

        #endregion

        #region 历史数据对比功能

        /// <summary>
        /// 保存数据到历史记录
        /// </summary>
        private void SaveToHistoryData()
        {
            try
            {
                // 确保历史数据目录存在
                Directory.CreateDirectory(historyDataPath);

                // 创建历史数据记录
                var historyRecord = new
                {
                    Id = Guid.NewGuid().ToString(),
                    SystemName = "增强版静态岩石力学参数法",
                    SaveTime = DateTime.Now,
                    DataSource = currentExcelFile ?? "手动输入",
                    DataPoints = chartDataPoints.Select(p => new
                    {
                        TopDepth = p.Depth,
                        BottomDepth = p.Depth,
                        BrittleIndex = p.BrittlenessIndex
                    }).ToList(),
                    TotalPoints = chartDataPoints.Count,
                    Statistics = GenerateDataStatistics(),
                    Version = "2.0"
                };

                // 生成文件名
                string fileName = $"History_{DateTime.Now:yyyyMMdd_HHmmss}_{historyRecord.Id.Substring(0, 8)}.json";
                string filePath = Path.Combine(historyDataPath, fileName);

                // 保存到文件
                string jsonData = Newtonsoft.Json.JsonConvert.SerializeObject(historyRecord, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(filePath, jsonData, Encoding.UTF8);

                LoggingService.Instance.Info($"历史数据已保存到: {filePath}，共 {chartDataPoints.Count} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"保存历史数据失败: {ex.Message}");
                throw;
            }
        }



        #endregion
    }
}
