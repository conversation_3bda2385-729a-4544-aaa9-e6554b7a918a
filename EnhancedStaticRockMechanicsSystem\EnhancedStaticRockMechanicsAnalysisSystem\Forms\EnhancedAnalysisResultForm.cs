using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    /// <summary>
    /// 增强分析结果可视化窗体
    /// </summary>
    public partial class EnhancedAnalysisResultForm : Form
    {
        private TabControl tabControl;
        private TabPage tabStatistics;
        private TabPage tabDistribution;
        private TabPage tabCorrelation;
        private TabPage tabRecommendations;
        
        private Chart chartStatistics;
        private Chart chartDistribution;
        private Chart chartCorrelation;
        private RichTextBox rtbRecommendations;
        
        private DataTable mechanicsData;
        private string analysisResult;

        public EnhancedAnalysisResultForm(DataTable data, string result)
        {
            mechanicsData = data;
            analysisResult = result;
            InitializeComponent();
            LoadAnalysisData();
        }

        private void InitializeComponent()
        {
            this.tabControl = new TabControl();
            this.tabStatistics = new TabPage();
            this.tabDistribution = new TabPage();
            this.tabCorrelation = new TabPage();
            this.tabRecommendations = new TabPage();
            this.chartStatistics = new Chart();
            this.chartDistribution = new Chart();
            this.chartCorrelation = new Chart();
            this.rtbRecommendations = new RichTextBox();

            this.SuspendLayout();

            // 窗体设置
            this.Text = "增强分析结果 - 可视化报告";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(33, 33, 33);
            this.ForeColor = Color.White;

            // TabControl设置
            this.tabControl.Dock = DockStyle.Fill;
            this.tabControl.BackColor = Color.FromArgb(45, 45, 45);
            this.tabControl.ForeColor = Color.White;
            this.tabControl.Font = new Font("微软雅黑", 10F);

            // 统计分析标签页
            this.tabStatistics.Text = "统计分析";
            this.tabStatistics.BackColor = Color.FromArgb(33, 33, 33);
            this.tabStatistics.Controls.Add(this.chartStatistics);

            // 分布分析标签页
            this.tabDistribution.Text = "分布分析";
            this.tabDistribution.BackColor = Color.FromArgb(33, 33, 33);
            this.tabDistribution.Controls.Add(this.chartDistribution);

            // 相关性分析标签页
            this.tabCorrelation.Text = "相关性分析";
            this.tabCorrelation.BackColor = Color.FromArgb(33, 33, 33);
            this.tabCorrelation.Controls.Add(this.chartCorrelation);

            // 建议标签页
            this.tabRecommendations.Text = "分析建议";
            this.tabRecommendations.BackColor = Color.FromArgb(33, 33, 33);
            this.tabRecommendations.Controls.Add(this.rtbRecommendations);

            // 图表设置
            SetupChart(this.chartStatistics);
            SetupChart(this.chartDistribution);
            SetupChart(this.chartCorrelation);

            // 文本框设置
            this.rtbRecommendations.Dock = DockStyle.Fill;
            this.rtbRecommendations.BackColor = Color.FromArgb(45, 45, 45);
            this.rtbRecommendations.ForeColor = Color.White;
            this.rtbRecommendations.Font = new Font("微软雅黑", 11F);
            this.rtbRecommendations.ReadOnly = true;

            // 添加标签页
            this.tabControl.TabPages.Add(this.tabStatistics);
            this.tabControl.TabPages.Add(this.tabDistribution);
            this.tabControl.TabPages.Add(this.tabCorrelation);
            this.tabControl.TabPages.Add(this.tabRecommendations);

            this.Controls.Add(this.tabControl);
            this.ResumeLayout(false);
        }

        private void SetupChart(Chart chart)
        {
            chart.Dock = DockStyle.Fill;
            chart.BackColor = Color.FromArgb(33, 33, 33);
            chart.BorderlineColor = Color.Gray;
            chart.BorderlineDashStyle = ChartDashStyle.Solid;
        }

        private void LoadAnalysisData()
        {
            try
            {
                CreateStatisticsChart();
                CreateDistributionChart();
                CreateCorrelationChart();
                LoadRecommendations();
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"加载分析数据失败: {ex.Message}");
                MessageBox.Show($"加载分析数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateStatisticsChart()
        {
            if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                return;

            chartStatistics.Series.Clear();
            chartStatistics.ChartAreas.Clear();
            chartStatistics.Legends.Clear();

            // 创建图表区域
            ChartArea chartArea = new ChartArea("StatisticsArea");
            chartArea.BackColor = Color.FromArgb(45, 45, 45);
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(100, 100, 100);
            chartArea.AxisX.Title = "参数类型";
            chartArea.AxisY.Title = "数值";
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartStatistics.ChartAreas.Add(chartArea);

            // 创建统计数据系列
            var avgSeries = new Series("平均值");
            avgSeries.ChartType = SeriesChartType.Column;
            avgSeries.Color = Color.Cyan;

            var maxSeries = new Series("最大值");
            maxSeries.ChartType = SeriesChartType.Column;
            maxSeries.Color = Color.Orange;

            var minSeries = new Series("最小值");
            minSeries.ChartType = SeriesChartType.Column;
            minSeries.Color = Color.LightGreen;

            // 计算统计数据
            if (mechanicsData.Columns.Contains("脆性指数/%"))
            {
                var brittlenessValues = mechanicsData.AsEnumerable()
                    .Where(row => row["脆性指数/%"] != DBNull.Value)
                    .Select(row => Convert.ToDouble(row["脆性指数/%"]))
                    .ToList();

                if (brittlenessValues.Count > 0)
                {
                    avgSeries.Points.AddXY("脆性指数", brittlenessValues.Average());
                    maxSeries.Points.AddXY("脆性指数", brittlenessValues.Max());
                    minSeries.Points.AddXY("脆性指数", brittlenessValues.Min());
                }
            }

            if (mechanicsData.Columns.Contains("静态杨氏模量/GPa"))
            {
                var esValues = mechanicsData.AsEnumerable()
                    .Where(row => row["静态杨氏模量/GPa"] != DBNull.Value)
                    .Select(row => Convert.ToDouble(row["静态杨氏模量/GPa"]))
                    .ToList();

                if (esValues.Count > 0)
                {
                    avgSeries.Points.AddXY("杨氏模量", esValues.Average());
                    maxSeries.Points.AddXY("杨氏模量", esValues.Max());
                    minSeries.Points.AddXY("杨氏模量", esValues.Min());
                }
            }

            if (mechanicsData.Columns.Contains("静态泊松比"))
            {
                var muSValues = mechanicsData.AsEnumerable()
                    .Where(row => row["静态泊松比"] != DBNull.Value)
                    .Select(row => Convert.ToDouble(row["静态泊松比"]))
                    .ToList();

                if (muSValues.Count > 0)
                {
                    avgSeries.Points.AddXY("泊松比", muSValues.Average());
                    maxSeries.Points.AddXY("泊松比", muSValues.Max());
                    minSeries.Points.AddXY("泊松比", muSValues.Min());
                }
            }

            chartStatistics.Series.Add(avgSeries);
            chartStatistics.Series.Add(maxSeries);
            chartStatistics.Series.Add(minSeries);

            // 添加图例
            Legend legend = new Legend("StatisticsLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            chartStatistics.Legends.Add(legend);
        }

        private void CreateDistributionChart()
        {
            if (mechanicsData == null || mechanicsData.Rows.Count == 0 || !mechanicsData.Columns.Contains("脆性指数/%"))
                return;

            chartDistribution.Series.Clear();
            chartDistribution.ChartAreas.Clear();
            chartDistribution.Legends.Clear();

            // 创建图表区域
            ChartArea chartArea = new ChartArea("DistributionArea");
            chartArea.BackColor = Color.FromArgb(45, 45, 45);
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.Title = "脆性指数范围";
            chartArea.AxisY.Title = "数据点数量";
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartDistribution.ChartAreas.Add(chartArea);

            // 创建分布数据系列
            var distributionSeries = new Series("脆性指数分布");
            distributionSeries.ChartType = SeriesChartType.Pie;

            // 统计脆性指数分布
            var brittlenessValues = mechanicsData.AsEnumerable()
                .Where(row => row["脆性指数/%"] != DBNull.Value)
                .Select(row => Convert.ToDouble(row["脆性指数/%"]))
                .ToList();

            int low = brittlenessValues.Count(v => v < 30);      // 低脆性
            int medium = brittlenessValues.Count(v => v >= 30 && v < 60); // 中脆性
            int high = brittlenessValues.Count(v => v >= 60);    // 高脆性

            if (low > 0)
            {
                var point = distributionSeries.Points.Add(low);
                point.LegendText = $"低脆性 (<30%) - {low}个";
                point.Color = Color.LightGreen;
            }

            if (medium > 0)
            {
                var point = distributionSeries.Points.Add(medium);
                point.LegendText = $"中脆性 (30-60%) - {medium}个";
                point.Color = Color.Orange;
            }

            if (high > 0)
            {
                var point = distributionSeries.Points.Add(high);
                point.LegendText = $"高脆性 (>60%) - {high}个";
                point.Color = Color.Red;
            }

            chartDistribution.Series.Add(distributionSeries);

            // 添加图例
            Legend legend = new Legend("DistributionLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            legend.Docking = Docking.Right;
            chartDistribution.Legends.Add(legend);
        }

        private void CreateCorrelationChart()
        {
            if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                return;

            chartCorrelation.Series.Clear();
            chartCorrelation.ChartAreas.Clear();
            chartCorrelation.Legends.Clear();

            // 创建图表区域
            ChartArea chartArea = new ChartArea("CorrelationArea");
            chartArea.BackColor = Color.FromArgb(45, 45, 45);
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.Gray;
            chartArea.AxisY.LineColor = Color.Gray;
            chartArea.AxisX.Title = "静态杨氏模量 (GPa)";
            chartArea.AxisY.Title = "脆性指数 (%)";
            chartArea.AxisX.TitleForeColor = Color.White;
            chartArea.AxisY.TitleForeColor = Color.White;
            chartCorrelation.ChartAreas.Add(chartArea);

            // 创建散点图系列
            var scatterSeries = new Series("杨氏模量 vs 脆性指数");
            scatterSeries.ChartType = SeriesChartType.Point;
            scatterSeries.Color = Color.Cyan;
            scatterSeries.MarkerStyle = MarkerStyle.Circle;
            scatterSeries.MarkerSize = 8;

            // 添加数据点
            if (mechanicsData.Columns.Contains("静态杨氏模量/GPa") && mechanicsData.Columns.Contains("脆性指数/%"))
            {
                foreach (DataRow row in mechanicsData.Rows)
                {
                    if (row["静态杨氏模量/GPa"] != DBNull.Value && row["脆性指数/%"] != DBNull.Value)
                    {
                        double es = Convert.ToDouble(row["静态杨氏模量/GPa"]);
                        double brittleness = Convert.ToDouble(row["脆性指数/%"]);
                        scatterSeries.Points.AddXY(es, brittleness);
                    }
                }
            }

            chartCorrelation.Series.Add(scatterSeries);

            // 添加图例
            Legend legend = new Legend("CorrelationLegend");
            legend.BackColor = Color.FromArgb(60, 60, 60);
            legend.ForeColor = Color.White;
            chartCorrelation.Legends.Add(legend);
        }

        private void LoadRecommendations()
        {
            rtbRecommendations.Text = analysisResult;
            
            // 设置富文本格式
            rtbRecommendations.SelectAll();
            rtbRecommendations.SelectionFont = new Font("微软雅黑", 11F);
            rtbRecommendations.SelectionColor = Color.White;
            rtbRecommendations.DeselectAll();
        }
    }
}
