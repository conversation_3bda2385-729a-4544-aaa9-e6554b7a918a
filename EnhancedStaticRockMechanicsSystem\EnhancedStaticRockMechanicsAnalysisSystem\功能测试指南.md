# EnhancedStaticRockMechanicsAnalysisSystem 功能测试指南

## 🎯 测试目标

验证所有修复的功能是否正常工作，包括曲线显示、数据导入、联动响应、缩放体验和历史对比等。

## 📋 测试步骤

### 1. 基础功能测试

#### 1.1 启动系统
- 运行程序，登录系统
- 进入"静态岩石力学参数法"模块
- 验证界面正常显示

#### 1.2 数据导入和曲线生成
- 点击"导入数据"按钮，选择Excel文件
- 验证数据正确导入到数据表中
- 点击"计算"按钮，生成脆性指数曲线
- **验证点：** 曲线应该正常显示在右侧图表中

### 2. SimpleComparisonChartForm曲线显示测试

#### 2.1 存为对比图功能
- 在主界面生成曲线后，点击"存为对比图"按钮
- **验证点：** 应该弹出SimpleComparisonChartForm窗体
- **验证点：** 曲线应该正常显示在对比图窗体中（问题1修复验证）

#### 2.2 导入数据功能测试
- 在SimpleComparisonChartForm中，点击"导入数据"按钮
- 测试导入Excel文件（.xlsx/.xls）
- 测试导入CSV文件（.csv）
- 测试导入JSON对比数据（.json）
- **验证点：** 不同格式的数据都能正确导入并显示为新的曲线

#### 2.3 矿物组分法数据绘制测试
- 导入包含"矿物"或"Mineral"关键词的数据文件
- **验证点：** 应该使用带数据点的线条样式（Circle标记）
- 导入其他数据
- **验证点：** 应该使用平滑曲线样式（无标记）

### 3. 联动功能测试

#### 3.1 数据表和图表联动
- 在主界面的数据表中点击任意行
- **验证点：** 右侧图表应该立即高亮对应的数据点（响应速度应该很快）
- **验证点：** 高亮点应该是金色圆圈，红色边框
- 连续点击不同行
- **验证点：** 高亮应该快速切换，没有延迟

### 4. 缩放功能测试

#### 4.1 Y轴缩放测试
- 在图表区域使用鼠标滚轮向上滚动
- **验证点：** Y轴（深度轴）应该放大，显示更少的深度范围
- 使用鼠标滚轮向下滚动
- **验证点：** Y轴应该缩小，显示更多的深度范围

#### 4.2 X轴缩放测试
- 按住Shift键，使用鼠标滚轮向上滚动
- **验证点：** X轴（脆性指数轴）应该放大，显示更小的脆性指数范围
- 按住Shift键，使用鼠标滚轮向下滚动
- **验证点：** X轴应该缩小，显示更大的脆性指数范围

#### 4.3 滚动条测试
- **验证点：** 图表应该显示X轴和Y轴的滚动条
- 拖动滚动条
- **验证点：** 图表视图应该相应移动

### 5. 历史数据对比功能测试

#### 5.1 历史数据保存测试
- 生成第一个曲线，点击"存为对比图"
- **验证点：** 数据应该同时保存到对比图和历史数据中
- 修改参数或导入不同数据，生成第二个曲线
- 再次点击"存为对比图"
- **验证点：** 应该保存第二个历史记录

#### 5.2 历史数据对比按钮测试
- 点击"历史数据对比"按钮
- **验证点：** 应该弹出历史数据选择窗体
- **验证点：** 窗体中应该显示之前保存的历史数据列表
- **验证点：** 每条记录应该显示：保存时间、数据源、数据点数、深度范围、脆性指数范围

#### 5.3 多选对比测试
- 在历史数据选择窗体中，勾选2-3个历史记录
- 点击"确定"按钮
- **验证点：** 应该打开对比图窗体，显示多条不同颜色的曲线
- **验证点：** 每条曲线应该有不同的颜色和标识

#### 5.4 历史数据管理测试
- 在历史数据选择窗体中，勾选一些记录
- 点击"删除选中"按钮
- **验证点：** 应该弹出确认对话框
- 确认删除
- **验证点：** 选中的记录应该被删除，列表更新

### 6. 综合场景测试

#### 6.1 同一深度不同地区对比
- 导入第一个地区的数据，生成曲线，存为对比图
- 导入第二个地区的数据，生成曲线，存为对比图
- 点击"历史数据对比"，选择这两个记录
- **验证点：** 应该能看到两个地区在同一深度的脆性指数差异

#### 6.2 不同算法对比
- 使用静态岩石力学参数法生成曲线，存为对比图
- 在SimpleComparisonChartForm中导入矿物组分法的数据
- **验证点：** 应该能看到两种算法的曲线样式不同（线条 vs 带标记点）

## 🔍 关键验证点总结

### 问题1修复验证
- ✅ SimpleComparisonChartForm中曲线正常显示

### 问题2修复验证  
- ✅ 导入数据按钮正常工作
- ✅ 支持多种数据格式导入
- ✅ 矿物组分法数据使用特殊绘制样式

### 问题3修复验证
- ✅ 数据表点击响应速度快
- ✅ 图表高亮效果明显
- ✅ 缩放功能流畅自然

### 新增功能验证
- ✅ 历史数据对比按钮正常工作
- ✅ 历史数据自动保存
- ✅ 多选历史数据对比
- ✅ 历史数据管理功能

## 🚨 注意事项

1. **数据格式要求：**
   - Excel/CSV文件应包含深度列和脆性指数列
   - 列名可以是中文（深度、脆性指数）或英文（depth、brittle）

2. **缩放操作：**
   - 普通滚轮：Y轴缩放
   - Shift+滚轮：X轴缩放
   - 缩放有最大和最小限制

3. **历史数据：**
   - 存储在用户AppData目录下
   - 最多可选择5个历史记录进行对比
   - 删除操作不可撤销

## 📊 性能指标

- 数据表点击响应时间：< 100ms
- 图表缩放响应时间：< 50ms  
- 数据导入处理时间：< 2s（1000行数据）
- 历史数据加载时间：< 1s（100个历史记录）

如果测试中发现任何问题，请记录具体的操作步骤和错误信息，以便进一步优化。
