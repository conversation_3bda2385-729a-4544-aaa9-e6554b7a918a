# 岩石力学参数法代码库
# Enhanced Static Rock Mechanics Analysis System - Complete Code Repository
# 创建时间: 2025-01-14
# 版本: v2.0.0 - 完整系统代码库
# 位置: F:\1-work\2025\2025-8\BritSystem\EnhancedStaticRockMechanicsSystem

## 系统概述
本代码库包含EnhancedStaticRockMechanicsAnalysisSystem系统的所有源代码文件，
包括窗体、核心逻辑、数据模型、服务类、配置文件等完整实现。

## 目录结构
```
EnhancedStaticRockMechanicsAnalysisSystem/
├── Program.cs                               # 程序入口
├── AppConfig.cs                             # 应用配置
├── EnhancedStaticRockMechanicsAnalysisSystem.csproj  # 项目文件
├── EnhancedStaticRockMechanicsAnalysisSystem.sln     # 解决方案文件
├── Forms/                                   # 窗体文件
│   ├── StaticRockMechanicsForm.cs          # 主分析窗体
│   ├── StaticRockMechanicsForm.Designer.cs # 主窗体设计器
│   ├── StaticRockMechanicsForm.resx        # 主窗体资源
│   ├── SimpleComparisonChartForm.cs        # 对比图表窗体
│   ├── SimpleComparisonChartForm.resx      # 对比图表资源
│   ├── DataTypeSelectionForm.cs            # 数据类型选择窗体
│   ├── HistoryComparisonSelectionForm.cs   # 历史数据选择窗体
│   ├── DataSearchForm.cs                   # 数据搜索窗体
│   ├── DataSearchForm.resx                 # 数据搜索资源
│   ├── EnhancedAnalysisResultForm.cs       # 增强分析结果窗体
│   ├── EnhancedAnalysisResultForm.resx     # 增强分析结果资源
│   ├── LoginForm.cs                        # 登录窗体
│   ├── LoginForm.Designer.cs               # 登录窗体设计器
│   ├── LoginForm.resx                      # 登录窗体资源
│   ├── DashboardForm.cs                    # 仪表板窗体
│   ├── DashboardForm.Designer.cs           # 仪表板设计器
│   └── DashboardForm.resx                  # 仪表板资源
├── Core/                                    # 核心功能
│   ├── RockMechanicsCalculator.cs          # 岩石力学计算器
│   ├── DataManager.cs                      # 数据管理器
│   └── FormAdapter.cs                      # 窗体适配器
├── Models/                                  # 数据模型
│   ├── RockMechanicsDataPoint.cs          # 岩石力学数据点
│   ├── BrittlenessDataPoint.cs            # 脆性数据点
│   ├── CalculationResult.cs               # 计算结果
│   └── RockMechanicsResult.cs             # 岩石力学结果
├── Services/                                # 服务类
│   ├── LoggingService.cs                   # 日志服务
│   ├── ImportService.cs                    # 导入服务
│   └── ExportService.cs                    # 导出服务
└── Resources/                               # 资源文件
```

## 编译状态
- ✅ 编译成功: 0个错误
- ⚠️ 警告信息: 106个警告（主要是nullable相关，不影响功能）
- ✅ 运行状态: 程序正常启动和运行
- ✅ 功能验证: 所有核心功能正常工作

## 最新修复
1. ✅ btnSaveCurve按钮功能已修复（添加事件处理和注册）
2. ✅ btnSeparate/btnRestore按钮激活逻辑已优化（支持历史对比激活）
3. ✅ 历史数据叠加对比功能完整实现
4. ✅ Y轴自适应刻度算法优化
5. ✅ 数据类型区分功能完整

===============================================================================
                              核心代码文件内容
===============================================================================

## 1. 项目配置文件
### EnhancedStaticRockMechanicsAnalysisSystem.csproj
```xml
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon />
    <StartupObject />
    <AssemblyTitle>增强版静态岩石力学参数分析系统</AssemblyTitle>
    <AssemblyDescription>基于静态岩石力学参数的脆性指数分析系统</AssemblyDescription>
    <AssemblyCompany>地质分析研究团队</AssemblyCompany>
    <AssemblyProduct>Enhanced Static Rock Mechanics Analysis System</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <!-- NuGet包引用 -->
  <ItemGroup>
    <PackageReference Include="System.Windows.Forms.DataVisualization" Version="1.0.0-prerelease.20110.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NPOI" Version="2.6.0" />
  </ItemGroup>

  <!-- 源代码文件 -->
  <ItemGroup>
    <!-- 核心类 -->
    <Compile Include="Core\RockMechanicsCalculator.cs" />
    <Compile Include="Core\DataManager.cs" />
    <Compile Include="Core\FormAdapter.cs" />

    <!-- 模型类 -->
    <Compile Include="Models\RockMechanicsDataPoint.cs" />
    <Compile Include="Models\BrittlenessDataPoint.cs" />
    <Compile Include="Models\CalculationResult.cs" />
    <Compile Include="Models\RockMechanicsResult.cs" />

    <!-- 服务类 -->
    <Compile Include="Services\ImportService.cs" />
    <Compile Include="Services\ExportService.cs" />
    <Compile Include="Services\LoggingService.cs" />

    <!-- 窗体类 -->
    <Compile Include="Forms\StaticRockMechanicsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\StaticRockMechanicsForm.Designer.cs">
      <DependentUpon>StaticRockMechanicsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SimpleComparisonChartForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DataTypeSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\HistoryComparisonSelectionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DataSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EnhancedAnalysisResultForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DashboardForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DashboardForm.Designer.cs">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </Compile>

    <!-- 主程序 -->
    <Compile Include="Program.cs" />
    <Compile Include="AppConfig.cs" />
  </ItemGroup>

  <!-- 资源文件 -->
  <ItemGroup>
    <EmbeddedResource Include="Forms\StaticRockMechanicsForm.resx">
      <DependentUpon>StaticRockMechanicsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SimpleComparisonChartForm.resx">
      <DependentUpon>SimpleComparisonChartForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DataSearchForm.resx">
      <DependentUpon>DataSearchForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EnhancedAnalysisResultForm.resx">
      <DependentUpon>EnhancedAnalysisResultForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DashboardForm.resx">
      <DependentUpon>DashboardForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\StaticRockMechanicsForm.zh-Hans-CN.resx">
      <DependentUpon>StaticRockMechanicsForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>

</Project>
```

## 2. 程序入口
### Program.cs
```csharp
using System;
using System.Windows.Forms;
using EnhancedStaticRockMechanicsAnalysisSystem.Forms;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;

namespace EnhancedStaticRockMechanicsAnalysisSystem
{
    /// <summary>
    /// 增强版静态岩石力学参数分析系统主程序
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // 启用应用程序视觉样式
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // 记录系统启动
                LoggingService.Instance.LogSystemStart();

                // 清理旧日志文件
                LoggingService.Instance.CleanupOldLogs();

                // 设置全局异常处理
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                LoggingService.Instance.Info("开始显示登录窗体");

                // 显示登录窗体
                using (var loginForm = new LoginForm())
                {
                    DialogResult loginResult = loginForm.ShowDialog();

                    if (loginResult == DialogResult.OK)
                    {
                        // 登录成功，显示主界面
                        string username = loginForm.Username;
                        LoggingService.Instance.LogUserAction(username, "登录成功");

                        using (var dashboardForm = new DashboardForm(username))
                        {
                            Application.Run(dashboardForm);
                        }
                    }
                    else
                    {
                        // 登录失败或取消，退出程序
                        LoggingService.Instance.Info("用户取消登录，程序退出");
                    }
                }

                // 记录系统关闭
                LoggingService.Instance.LogSystemShutdown();
            }
            catch (Exception ex)
            {
                // 记录启动失败
                LoggingService.Instance.Error("系统启动失败", ex);

                // 显示错误消息
                MessageBox.Show($"系统启动失败:\n{ex.Message}\n\n详细信息请查看日志文件。",
                    "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理UI线程异常
        /// </summary>
        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            try
            {
                LoggingService.Instance.Error("UI线程发生未处理异常", e.Exception);

                string message = $"程序发生错误:\n{e.Exception.Message}\n\n是否继续运行程序？";
                DialogResult result = MessageBox.Show(message, "程序错误",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Error);

                if (result == DialogResult.No)
                {
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                // 如果连日志都无法写入，直接显示消息框
                MessageBox.Show($"严重错误:\n{ex.Message}", "系统错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        /// <summary>
        /// 处理非UI线程异常
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception exception)
                {
                    LoggingService.Instance.Error("应用程序域发生未处理异常", exception);

                    string message = $"程序发生严重错误:\n{exception.Message}\n\n程序将退出。";
                    MessageBox.Show(message, "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                else
                {
                    LoggingService.Instance.Error($"应用程序域发生未知异常: {e.ExceptionObject}");
                    MessageBox.Show("程序发生未知错误，程序将退出。", "严重错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch
            {
                // 最后的保护措施
                MessageBox.Show("程序发生严重错误，程序将退出。", "严重错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                Environment.Exit(1);
            }
        }
    }
}
```

## 3. 应用配置
### AppConfig.cs
```csharp
using System;
using System.Configuration;
using System.IO;
using Newtonsoft.Json;

namespace EnhancedStaticRockMechanicsAnalysisSystem
{
    /// <summary>
    /// 应用程序配置管理
    /// </summary>
    public class AppConfig
    {
        private static AppConfig? _instance;
        private static readonly object _lock = new object();
        private readonly string _configFilePath;

        // 配置属性
        public string ApplicationName { get; set; } = "增强版静态岩石力学参数分析系统";
        public string Version { get; set; } = "1.0.0";
        public string DatabaseConnectionString { get; set; } = "";
        public bool EnableLogging { get; set; } = true;
        public int LogRetentionDays { get; set; } = 30;
        public string DefaultExportPath { get; set; } = "";
        public string DefaultImportPath { get; set; } = "";
        public bool AutoSaveEnabled { get; set; } = true;
        public int AutoSaveIntervalMinutes { get; set; } = 5;
        public string Theme { get; set; } = "Dark";
        public string Language { get; set; } = "zh-CN";

        // 计算参数配置
        public double DefaultDensityMin { get; set; } = 1.5;
        public double DefaultDensityMax { get; set; } = 5.0;
        public double DefaultVpMin { get; set; } = 1000;
        public double DefaultVpMax { get; set; } = 8000;
        public double DefaultVsMin { get; set; } = 500;
        public double DefaultVsMax { get; set; } = 5000;
        public double DefaultVpVsRatioMin { get; set; } = 1.4;
        public double DefaultVpVsRatioMax { get; set; } = 2.5;

        // 脆性指数计算参数
        public double BrittlenessEMin { get; set; } = 10.0;
        public double BrittlenessEMax { get; set; } = 80.0;
        public double BrittlenessVMin { get; set; } = 0.15;
        public double BrittlenessVMax { get; set; } = 0.35;

        private AppConfig()
        {
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            LoadConfiguration();
        }

        /// <summary>
        /// 获取配置实例
        /// </summary>
        public static AppConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new AppConfig();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    string json = File.ReadAllText(_configFilePath);
                    var config = JsonConvert.DeserializeObject<AppConfig>(json);
                    if (config != null)
                    {
                        CopyPropertiesFrom(config);
                    }
                }
                else
                {
                    // 创建默认配置
                    SetDefaultPaths();
                    SaveConfiguration();
                }
            }
            catch (Exception ex)
            {
                // 如果加载失败，使用默认配置
                SetDefaultPaths();
                Console.WriteLine($"加载配置文件失败，使用默认配置: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置默认路径
        /// </summary>
        private void SetDefaultPaths()
        {
            string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            string appFolder = Path.Combine(documentsPath, ApplicationName);

            DefaultExportPath = Path.Combine(appFolder, "导出数据");
            DefaultImportPath = Path.Combine(appFolder, "导入数据");

            // 创建目录
            try
            {
                Directory.CreateDirectory(DefaultExportPath);
                Directory.CreateDirectory(DefaultImportPath);
            }
            catch
            {
                // 如果创建失败，使用当前目录
                DefaultExportPath = AppDomain.CurrentDomain.BaseDirectory;
                DefaultImportPath = AppDomain.CurrentDomain.BaseDirectory;
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfiguration()
        {
            try
            {
                string json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存配置文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从另一个配置对象复制属性
        /// </summary>
        /// <param name="source"></param>
        private void CopyPropertiesFrom(AppConfig source)
        {
            ApplicationName = source.ApplicationName;
            Version = source.Version;
            DatabaseConnectionString = source.DatabaseConnectionString;
            EnableLogging = source.EnableLogging;
            LogRetentionDays = source.LogRetentionDays;
            DefaultExportPath = source.DefaultExportPath;
            DefaultImportPath = source.DefaultImportPath;
            AutoSaveEnabled = source.AutoSaveEnabled;
            AutoSaveIntervalMinutes = source.AutoSaveIntervalMinutes;
            Theme = source.Theme;
            Language = source.Language;

            DefaultDensityMin = source.DefaultDensityMin;
            DefaultDensityMax = source.DefaultDensityMax;
            DefaultVpMin = source.DefaultVpMin;
            DefaultVpMax = source.DefaultVpMax;
            DefaultVsMin = source.DefaultVsMin;
            DefaultVsMax = source.DefaultVsMax;
            DefaultVpVsRatioMin = source.DefaultVpVsRatioMin;
            DefaultVpVsRatioMax = source.DefaultVpVsRatioMax;

            BrittlenessEMin = source.BrittlenessEMin;
            BrittlenessEMax = source.BrittlenessEMax;
            BrittlenessVMin = source.BrittlenessVMin;
            BrittlenessVMax = source.BrittlenessVMax;
        }

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        public void ResetToDefault()
        {
            ApplicationName = "增强版静态岩石力学参数分析系统";
            Version = "1.0.0";
            DatabaseConnectionString = "";
            EnableLogging = true;
            LogRetentionDays = 30;
            AutoSaveEnabled = true;
            AutoSaveIntervalMinutes = 5;
            Theme = "Dark";
            Language = "zh-CN";

            DefaultDensityMin = 1.5;
            DefaultDensityMax = 5.0;
            DefaultVpMin = 1000;
            DefaultVpMax = 8000;
            DefaultVsMin = 500;
            DefaultVsMax = 5000;
            DefaultVpVsRatioMin = 1.4;
            DefaultVpVsRatioMax = 2.5;

            BrittlenessEMin = 10.0;
            BrittlenessEMax = 80.0;
            BrittlenessVMin = 0.15;
            BrittlenessVMax = 0.35;

            SetDefaultPaths();
            SaveConfiguration();
        }

        /// <summary>
        /// 验证配置有效性
        /// </summary>
        /// <returns></returns>
        public (bool IsValid, string ErrorMessage) ValidateConfiguration()
        {
            if (LogRetentionDays < 1 || LogRetentionDays > 365)
                return (false, "日志保留天数必须在1-365之间");

            if (AutoSaveIntervalMinutes < 1 || AutoSaveIntervalMinutes > 60)
                return (false, "自动保存间隔必须在1-60分钟之间");

            if (DefaultDensityMin >= DefaultDensityMax)
                return (false, "密度最小值必须小于最大值");

            if (DefaultVpMin >= DefaultVpMax)
                return (false, "纵波速度最小值必须小于最大值");

            if (DefaultVsMin >= DefaultVsMax)
                return (false, "横波速度最小值必须小于最大值");

            if (BrittlenessEMin >= BrittlenessEMax)
                return (false, "脆性指数杨氏模量范围设置错误");

            if (BrittlenessVMin >= BrittlenessVMax)
                return (false, "脆性指数泊松比范围设置错误");

            return (true, "配置验证通过");
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns></returns>
        public string GetConfigFilePath()
        {
            return _configFilePath;
        }
    }
}
```

## 4. 核心计算器
### Core/RockMechanicsCalculator.cs
```csharp
using System;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Core
{
    /// <summary>
    /// 岩石力学参数计算器
    /// </summary>
    public class RockMechanicsCalculator
    {
        /// <summary>
        /// 计算脆性指数和相关参数
        /// </summary>
        /// <param name="density">密度 (g/cm³)</param>
        /// <param name="vpVelocity">纵波速度 (m/s)</param>
        /// <param name="vsVelocity">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        public CalculationResult CalculateBrittleness(double density, double vpVelocity, double vsVelocity)
        {
            try
            {
                // 验证输入参数
                if (density <= 0)
                    return CalculationResult.CreateFailure("密度必须大于0");

                if (vpVelocity <= 0)
                    return CalculationResult.CreateFailure("纵波速度必须大于0");

                if (vsVelocity <= 0)
                    return CalculationResult.CreateFailure("横波速度必须大于0");

                if (vpVelocity <= vsVelocity)
                    return CalculationResult.CreateFailure("纵波速度必须大于横波速度");

                var result = new CalculationResult();

                // 记录输入参数
                result.AddInputParameter("密度", density);
                result.AddInputParameter("纵波速度", vpVelocity);
                result.AddInputParameter("横波速度", vsVelocity);

                // 转换密度单位：g/cm³ -> kg/m³
                double densityKgM3 = density * 1000;
                result.AddCalculationStep($"密度转换: {density} g/cm³ = {densityKgM3} kg/m³");

                // 计算动态弹性模量
                // 剪切模量 G = ρ * Vs²
                double shearModulus = densityKgM3 * Math.Pow(vsVelocity, 2) / 1e9; // 转换为GPa
                result.ShearModulus = shearModulus;
                result.AddCalculationStep($"剪切模量 G = ρ × Vs² = {densityKgM3} × {vsVelocity}² = {shearModulus:F2} GPa");

                // 体积模量 K = ρ * (Vp² - 4/3 * Vs²)
                double bulkModulus = densityKgM3 * (Math.Pow(vpVelocity, 2) - (4.0 / 3.0) * Math.Pow(vsVelocity, 2)) / 1e9; // 转换为GPa
                result.BulkModulus = bulkModulus;
                result.AddCalculationStep($"体积模量 K = ρ × (Vp² - 4/3×Vs²) = {bulkModulus:F2} GPa");

                // 杨氏模量 E = 9KG/(3K+G)
                double youngModulus = (9 * bulkModulus * shearModulus) / (3 * bulkModulus + shearModulus);
                result.YoungModulus = youngModulus;
                result.AddCalculationStep($"杨氏模量 E = 9KG/(3K+G) = {youngModulus:F2} GPa");

                // 泊松比 ν = (3K-2G)/(6K+2G)
                double poissonRatio = (3 * bulkModulus - 2 * shearModulus) / (6 * bulkModulus + 2 * shearModulus);
                result.PoissonRatio = poissonRatio;
                result.AddCalculationStep($"泊松比 ν = (3K-2G)/(6K+2G) = {poissonRatio:F3}");

                // 脆性指数计算 - 注意：单点计算无法使用动态范围
                // 这里使用固定范围作为默认值，实际批量计算时会使用动态范围
                double E_min = 5.0;   // GPa - 默认范围
                double E_max = 80.0;  // GPa - 默认范围
                double v_min = 0.1;   // 默认范围
                double v_max = 0.4;   // 默认范围

                // 归一化杨氏模量脆性指数
                // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
                double EBRIT = 0;
                if (Math.Abs(E_max - E_min) > 1e-10)
                {
                    EBRIT = (youngModulus - E_min) / (E_max - E_min) * 100;
                    EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内
                }

                // 归一化泊松比脆性指数
                // PBRIT = (μmax - μs) / (μmax - μmin) × 100%
                double PBRIT = 0;
                if (Math.Abs(v_max - v_min) > 1e-10)
                {
                    PBRIT = (v_max - poissonRatio) / (v_max - v_min) * 100;
                    PBRIT = Math.Max(0, Math.Min(100, PBRIT)); // 限制在0-100%范围内
                }

                // 综合脆性指数
                // BRIT = (EBRIT + PBRIT) / 2
                double brittlenessIndex = (EBRIT + PBRIT) / 2;

                result.BrittlenessIndex = brittlenessIndex;
                result.AddCalculationStep($"归一化杨氏模量脆性指数 EBRIT = {EBRIT:F2}%");
                result.AddCalculationStep($"归一化泊松比脆性指数 PBRIT = {PBRIT:F2}%");
                result.AddCalculationStep($"综合脆性指数 BI = {brittlenessIndex:F2}% (注意：单点计算使用默认范围)");

                // 验证结果
                if (!result.IsValidResult())
                {
                    return CalculationResult.CreateFailure("计算结果超出合理范围");
                }

                result.AddCalculationStep("计算完成");
                return result;
            }
            catch (Exception ex)
            {
                return CalculationResult.CreateFailure($"计算过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算脆性指数和相关参数（使用RockMechanicsDataPoint）
        /// </summary>
        /// <param name="dataPoint">岩石力学数据点</param>
        /// <returns>计算结果</returns>
        public CalculationResult CalculateBrittlenessIndex(RockMechanicsDataPoint dataPoint)
        {
            return CalculateBrittleness(dataPoint.Density, dataPoint.VpVelocity, dataPoint.VsVelocity);
        }

        /// <summary>
        /// 计算Vp/Vs比值
        /// </summary>
        /// <param name="vpVelocity">纵波速度</param>
        /// <param name="vsVelocity">横波速度</param>
        /// <returns>Vp/Vs比值</returns>
        public double CalculateVpVsRatio(double vpVelocity, double vsVelocity)
        {
            if (vsVelocity == 0)
                throw new ArgumentException("横波速度不能为0");

            return vpVelocity / vsVelocity;
        }

        /// <summary>
        /// 根据脆性指数评估岩石类型
        /// </summary>
        /// <param name="brittlenessIndex">脆性指数</param>
        /// <returns>岩石类型描述</returns>
        public string EvaluateRockType(double brittlenessIndex)
        {
            if (brittlenessIndex >= 0.8)
                return "极脆性岩石";
            else if (brittlenessIndex >= 0.6)
                return "高脆性岩石";
            else if (brittlenessIndex >= 0.4)
                return "中等脆性岩石";
            else if (brittlenessIndex >= 0.2)
                return "低脆性岩石";
            else
                return "韧性岩石";
        }

        /// <summary>
        /// 验证输入参数的合理性
        /// </summary>
        /// <param name="density">密度</param>
        /// <param name="vpVelocity">纵波速度</param>
        /// <param name="vsVelocity">横波速度</param>
        /// <returns>验证结果</returns>
        public (bool IsValid, string ErrorMessage) ValidateInputParameters(double density, double vpVelocity, double vsVelocity)
        {
            if (density < 1.5 || density > 5.0)
                return (false, "密度应在1.5-5.0 g/cm³范围内");

            if (vpVelocity < 1000 || vpVelocity > 8000)
                return (false, "纵波速度应在1000-8000 m/s范围内");

            if (vsVelocity < 500 || vsVelocity > 5000)
                return (false, "横波速度应在500-5000 m/s范围内");

            if (vpVelocity / vsVelocity < 1.4 || vpVelocity / vsVelocity > 2.5)
                return (false, "Vp/Vs比值应在1.4-2.5范围内");

            return (true, "参数验证通过");
        }
    }
}
```

## 5. 日志服务
